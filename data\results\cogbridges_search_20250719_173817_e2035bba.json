{"query": "Should I subscribe to <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, or <PERSON>?", "session_id": "20250719_173817_e2035bba", "timestamp": "2025-07-19T17:38:17.555167", "google_results": [{"title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspective on AI ...", "url": "https://www.reddit.com/r/ClaudeAI/comments/1dqj1lg/claude_35_sonnet_vs_gpt4_a_programmers/", "snippet": "Jun 28, 2024 ... Overall Experience: While I was initially excited about GPT-4's release (ChatGPT was my first-ever online subscription), using <PERSON> has ...", "rank": 1}, {"title": "How does <PERSON>, <PERSON><PERSON> or <PERSON>lam<PERSON> compare to GPT or Claude right ...", "url": "https://www.reddit.com/r/singularity/comments/1h8ox94/how_does_gemini_grok_or_llama_compare_to_gpt_or/", "snippet": "Dec 7, 2024 ... Right now I'm using GPT 4o and Claude 3.5 sonnet and I'm curious how any other model compare to those two? Gemini 1.5 pro, Llama 3.1/3.2 or Grok beta?", "rank": 2}, {"title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet (thinking): Google ...", "url": "https://www.reddit.com/r/ClaudeAI/comments/1jnl8pn/i_tested_gemini_25_pro_against_claude_37_sonnet/", "snippet": "Mar 30, 2025 ... I could be biased af, but it was my observation. For a detailed comparison (also with Grok ... Gemini 2.5 Pro vs Claude 3.7 Sonnet (thinking).", "rank": 3}, {"title": "SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude : r/singularity", "url": "https://www.reddit.com/r/singularity/comments/1lwdzjd/svg_benchmark_grok_vs_gemini_vs_chatgpt_vs_claude/", "snippet": "Jul 11, 2025 ... I mainly wanted to test Grok 4 against the others to see if it really was such a jump given its results in other benchmarks, but I must say I'm ...", "rank": 4}, {"title": "Would you say Grok 3 is better than GPT 4.5 or Claude 3.7 : r/grok", "url": "https://www.reddit.com/r/grok/comments/1j42134/would_you_say_grok_3_is_better_than_gpt_45_or/", "snippet": "Mar 5, 2025 ... Hello! I'm on SuperGrok too, and I also have access to my company's GPT Pro account and subscribed to <PERSON> just out of curiosity (I' ...", "rank": 5}], "google_search_time": 1.****************, "reddit_posts": [{"success": true, "google_result": {"title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspective on AI ...", "url": "https://www.reddit.com/r/ClaudeAI/comments/1dqj1lg/claude_35_sonnet_vs_gpt4_a_programmers/", "snippet": "Jun 28, 2024 ... Overall Experience: While I was initially excited about GPT-4's release (ChatGPT was my first-ever online subscription), using <PERSON> has ...", "rank": 1}, "post": {"id": "1dqj1lg", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspective on AI assistants", "author": "<PERSON><PERSON><PERSON><PERSON>", "score": 226, "num_comments": 139, "subreddit": "ClaudeAI", "url": "https://www.reddit.com/r/ClaudeAI/comments/1dqj1lg/claude_35_sonnet_vs_gpt4_a_programmers/", "created_utc": **********.0, "selftext": "As a subscriber to both <PERSON> and ChatGPT, I've been comparing their performance to decide which one to keep. Here's my experience:\n\nCoding: As a programmer, I've found <PERSON> to be exceptionally impressive. In my experience, it consistently produces nearly bug-free code on the first try, outperforming GPT-4 in this area.\n\nText Summarization: I recently tested both models on summarizing a PDF of my monthly spending transactions. <PERSON>'s summary was not only more accurate but also delivered in a smart, human-like style. In contrast, GPT-4's summary contained errors and felt robotic and unengaging.\n\nOverall Experience: While I was initially excited about GPT-4's release (ChatGPT was my first-ever online subscription), using <PERSON> has changed my perspective. Returning to GPT-4 after using <PERSON> feels like a step backward, reminiscent of using GPT-3.5.\n\nIn conclusion, Claude 3.5 Sonnet has impressed me with its coding prowess, accurate summarization, and natural communication style. It's challenging my assumption that GPT-4 is the current \"state of the art\" in AI language models.\n\nI'm curious to hear about others' experiences. Have you used both models? How do they compare in your use cases?"}, "comments": [{"id": "laob<PERSON><PERSON>", "author": "haslo", "body": "Yes. <PERSON> is better in every aspect except for limits.\n\nSo I currently have both. Using <PERSON> as long as I can, selectively with longer prompts. And ChatGPT for the rest.\n\nI did have two ChatGPT subscriptions. Killed one of them for <PERSON>.", "score": 56, "created_utc": 1719580563.0}, {"id": "laob1hl", "author": "Kathane37", "body": "Test Claude on graph information retrieval\nThis is an other hidden skill where it crush 4o", "score": 15, "created_utc": 1719580188.0}, {"id": "lap29cr", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "The post reads like its written by <PERSON> as well. <PERSON><PERSON><PERSON>", "score": 29, "created_utc": 1719589853.0}, {"id": "laoije3", "author": "MarinatedTechnician", "body": "Same, I still have both (pro). And I am considering dumping ChatGPT for <PERSON>.\n\nBut the reason I'm not putting the finger on the cancel button yet, is because I really love the Dall-E integration with ChatGPT. <PERSON> has a very limited available set of cool plugins to play with out of the box, sure you can probably connect it back-end with something, but I don't have time for experimenting with those things.\n\nWith claude I mashed together a fully functional game in 5 hours\n\nWith ChatGPT I didnt even get a decent Blender Greeble script. It got things wrong with code over and over again.\n\nBut ChatGPT has all of these fun plug-ins I can play with, and they are truly fun.\n\nKeeping both for now.", "score": 7, "created_utc": 1719583079.0}, {"id": "larv1y4", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Well, I know nothing about code, not a single thing. I managed to create two games using python and running them in VS Code using <PERSON> to not only generate the code, but I also helped me troubleshoot the issues I was having. It walked me through each code update when asking it to “make it better.” I’m currently trying to decide between <PERSON> of ChatGPT for my next step.", "score": 8, "created_utc": 1719626674.0}, {"id": "laotmqh", "author": "OfficeSalamander", "body": "Yeah, I find <PERSON> FAR more correct. I basically don't even use ChatGPT anymore", "score": 6, "created_utc": 1719586979.0}], "commenters": ["Kathane37", "OfficeSalamander", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MarinatedTechnician", "haslo", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"success": true, "google_result": {"title": "How does <PERSON>, <PERSON><PERSON> or <PERSON>lam<PERSON> compare to GPT or Claude right ...", "url": "https://www.reddit.com/r/singularity/comments/1h8ox94/how_does_gemini_grok_or_llama_compare_to_gpt_or/", "snippet": "Dec 7, 2024 ... Right now I'm using GPT 4o and Claude 3.5 sonnet and I'm curious how any other model compare to those two? Gemini 1.5 pro, Llama 3.1/3.2 or Grok beta?", "rank": 2}, "post": {"id": "1h8ox94", "title": "How does <PERSON>, <PERSON><PERSON> or <PERSON><PERSON><PERSON> compare to GP<PERSON> or <PERSON> right now?", "author": "shun_master23", "score": 17, "num_comments": 13, "subreddit": "singularity", "url": "https://www.reddit.com/r/singularity/comments/1h8ox94/how_does_gemini_grok_or_llama_compare_to_gpt_or/", "created_utc": 1733563557.0, "selftext": "Right now I'm using GPT 4o and Claude 3.5 sonnet and I'm curious how any other model compare to those two? Gemini 1.5 pro, Llama 3.1/3.2 or Grok beta? What about mistral is it any good? I'm curious if anthropic and OpenAI has any actual competition on the market"}, "comments": [{"id": "m0v67t2", "author": "[deleted]", "body": "I have been using Sonnet 3.6 for a ton text book summarization, citation finding/creating, and paper writing assistance. Very heavy user since summer and often large context windows.\n\nGemini 1206 large contexts aren't working yet (or weren't last night), but from what I can tell so far from a few tests, the writing is extremely good. And from what I've seen, the coding is excellent.\n\nI would imagine openai and anthropic are a bit nervous mostly due to Google's aggressive pricing. Even moreso once these models go 2M context windows over API.", "score": 4, "created_utc": 1733578157.0}, {"id": "m0ve2er", "author": "<PERSON><PERSON><PERSON><PERSON>", "body": "Tier 1:  gemini exp 12.06 , o1, <PERSON> 3.6 sonnet  \nTier 2: gpt4o, o1 mini  \nTier 3: Grok 2 beta, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "score": 11, "created_utc": 1733581325.0}, {"id": "m0uq4go", "author": "Objective_Lab_3182", "body": "Grok 2 is behind at the moment, but it seems to me to be the model that has the least censorship. \nIn fact, Grok 2 is available for free. \nGrok 3 is close to being released.", "score": 4, "created_utc": 1733569974.0}, {"id": "m0xr9p3", "author": "Its_not_a_tumor", "body": "Llama 3.3 came out yesterday. It's better but still not Claude3.5 quality", "score": 1, "created_utc": 1733609187.0}, {"id": "m1ehc9b", "author": "focal-fossa", "body": "I'm integrating APIs. Can't tell about their websites and apps.  \n  \nComparing to GPT-4o API:  \n\\- Llama 3 is perfect for real-time data and it also provides sources.   \n\\- xAI Grok Beta has less censorship. I tried  Grok Vision and was able to upload even adult images.", "score": 1, "created_utc": 1733857433.0}, {"id": "m0uh58v", "author": "Cagnazzo82", "body": "<PERSON><PERSON> and <PERSON> are amazing, but in different ways. I think <PERSON> is right behind them... although it's way ahead in terms of context window. I just prefer GPT and <PERSON>'s outputs in general. <PERSON> has the best personality, ChatGPT is the swiss army knife. Gemini is probably the best for studying and research... especially if you use it via other tools like NotebookLM.\n\nI can't speak on Grok or Llama since I don't have unique use-cases for either.", "score": 1, "created_utc": 1733564319.0}], "commenters": ["Objective_Lab_3182", "Cagnazzo82", "focal-fossa", "Its_not_a_tumor", "<PERSON><PERSON><PERSON><PERSON>"]}, {"success": true, "google_result": {"title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet (thinking): Google ...", "url": "https://www.reddit.com/r/ClaudeAI/comments/1jnl8pn/i_tested_gemini_25_pro_against_claude_37_sonnet/", "snippet": "Mar 30, 2025 ... I could be biased af, but it was my observation. For a detailed comparison (also with Grok ... Gemini 2.5 Pro vs Claude 3.7 Sonnet (thinking).", "rank": 3}, "post": {"id": "1jnl8pn", "title": "I tested Gemini 2.5 Pro against <PERSON> 3.7 Sonnet (thinking): Google is clearly after <PERSON><PERSON><PERSON>'s lunch", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "score": 555, "num_comments": 162, "subreddit": "ClaudeAI", "url": "https://www.reddit.com/r/ClaudeAI/comments/1jnl8pn/i_tested_gemini_25_pro_against_claude_37_sonnet/", "created_utc": 1743365136.0, "selftext": "Gemini 2.5 Pro surprised everyone; nobody expected Google to release the state-of-the-art model out of the blue. This time, it is pretty clear they went straight after the developer's market, where <PERSON> has been reigning for almost a year. This was their best bet to regain their reputation. Total <PERSON> victory here.\n\nAs a long-time Claude user, I wanted to know how good <PERSON> is compared to 3.7 Sonnet thinking, which is the best among the existing thinking models. \n\nAnd here are some observations.\n\n# Where does Gemini lead?\n\n* Code generation in Gemini 2.5 Pro for most day-to-day tasks is better than that of Claude 3.7 Sonnet. Not sure about esoteric use cases.\n* One million in context window is a huge plus. I think Google Deepmind is the only company that has cracked the context window problem even Gemma 27b was great at it.\n* Ai Studio sucks, but it's free and is a huge boost for quick adoption. Claude 3.7 Sonnet (thinking) is not available for free users.\n\n# Where does <PERSON> lead?\n\n* Reasoning in Claude 3.7 Sonnet is more nuanced and streamlined. It is better than Gemini 2.5 Pro. \n* I am not sure how to explain it, but for some reason, <PERSON> is obedient and does what is asked for, and <PERSON> feels more agentic. I could be biased af, but it was my observation.\n\nFor a detailed comparison (also with Grok 3 think), check out the blog post: [Gemini 2.5 Pro vs Grok 3 vs Claude 3.7 Sonnet](https://composio.dev/blog/gemini-2-5-pro-vs-claude-3-7-sonnet-vs-grok-3/)\n\nFor some more examples of coding tasks: [Gemini 2.5 Pro vs Claude 3.7 Sonnet (thinking)](https://composio.dev/blog/gemini-2-5-pro-vs-claude-3-7-sonnet-coding-comparison/)\n\nGoogle, at this point, seems more of a threat to Anthropic than OpenAI. \n\nOpenAI has the biggest DAU among the AI leaders, and their offering is more diverse, catering to multiple professionals. Anthropic, on the other hand, is more developer-focused, the only professionals who will switch to a better and cheaper option in a heartbeat. And at present, Gemini offers more than Claude.\n\nIt would be interesting to see how Anthropic navigates this.\n\nAs someone who still uses Claude, I would like to know your thoughts on Gemini 2.5 Pro and where you have found it better and worse than Sonnet."}, "comments": [{"id": "mkkxbya", "author": "Chogo82", "body": "Google even open sourced how they achieve the big context window but no one can seem to catch up. I wonder if it has to do with their TPU architecture.", "score": 102, "created_utc": 1743368429.0}, {"id": "mkku21b", "author": "[deleted]", "body": "I've been comparing them all week in PhD level math + programming for a big research project. 2.5 pro is next level. Smartest LLM ever", "score": 61, "created_utc": 1743367398.0}, {"id": "mkknpk8", "author": "xAragon_", "body": "I think there should be a new benchmark, just counting these meaningless \"benchmark\" posts on r/ClaudeAI and checking which model has more posts claiming it's better.", "score": 75, "created_utc": 1743365454.0}, {"id": "mkktnqn", "author": "Mr<PERSON><PERSON><PERSON>", "body": "i have a react app made previously with sonnet 3.5, i tested both 3.7 and gemini 2.5 to change how an animation was made. both of them failed but gemini failed worst not giving working code at all  i burned like 130k tokens and it was unable to made a usable output. claude 3.7 after some iterations found the solution and delivered. to be completely honest, the solution was provided by sonnet 3.5 in a mockup test i made earlier, 3.7 expanded from this solution", "score": 9, "created_utc": 1743367275.0}, {"id": "mkkzvpl", "author": "Heavy_Hunt7860", "body": "I like that you wrote this and not <PERSON> or <PERSON>\n\nWell organized human writing is a rare commodity these days. \n\nAm also impressed with Gemini 2.5. I have relegated <PERSON> 3.7 thinking to support and am letting <PERSON> handle the bulk of tasks.", "score": 9, "created_utc": 1743369257.0}, {"id": "mkkrjsv", "author": "Sad_Run_9798", "body": "I agree 2.5 is quite good, but it’s also sort of uncooperative. I told it to do a thing in cursor and it thought about it, then proceeded to tell me basically “that would be too complicated we should leave it as it is” and just didn’t do it. I reran the prompt with <PERSON> and it was done easily. Wasn’t even complicated.", "score": 25, "created_utc": 1743366623.0}], "commenters": ["xAragon_", "Sad_Run_9798", "Chogo82", "Heavy_Hunt7860", "Mr<PERSON><PERSON><PERSON>"]}, {"success": true, "google_result": {"title": "SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude : r/singularity", "url": "https://www.reddit.com/r/singularity/comments/1lwdzjd/svg_benchmark_grok_vs_gemini_vs_chatgpt_vs_claude/", "snippet": "Jul 11, 2025 ... I mainly wanted to test Grok 4 against the others to see if it really was such a jump given its results in other benchmarks, but I must say I'm ...", "rank": 4}, "post": {"id": "1lwdzjd", "title": "SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude", "author": "en<PERSON>a", "score": 324, "num_comments": 90, "subreddit": "singularity", "url": "https://www.reddit.com/gallery/1lwdzjd", "created_utc": 1752157315.0, "selftext": "I tested different LLMs to check their ability to create SVG images in different ways. I believe this is a good way to test for their visual and spatial reasoning (which will be essential for AGI). It's a field where there's still lots of improvement to be had and there isn't as much available testing data for training. It's all one shot and with no tools.\n\nDidn't use Claude Opus because it's too expensive, and I didn't use other models because I wanted to limit it to these four that are recent and priced around the same range. I mainly wanted to test Grok 4 against the others to see if it really was such a jump given its results in other benchmarks, but I must say I'm disappointed in its results here."}, "comments": [{"id": "n2dg2sr", "author": "freedomheaven", "body": "Looks like Gemini 2.5 is clear winner here.", "score": 207, "created_utc": 1752159074.0}, {"id": "n2dn6q4", "author": "vasilenko93", "body": "So during yesterday’s livestream Elon mentioned multiple times that <PERSON><PERSON>’s image perception sucks. They are still working on the vision portion and will have a video model in a few months. They mentioned <PERSON><PERSON> will be able to take video as input when done.", "score": 28, "created_utc": 1752161053.0}, {"id": "n2df493", "author": "Axelwickm", "body": "Grok 4 certainly is not strong here", "score": 69, "created_utc": 1752158804.0}, {"id": "n2dnsz1", "author": "metaphorician", "body": "https://preview.redd.it/oxirbtkqe2cf1.png?width=1164&format=png&auto=webp&s=53f58638f5d434a89183966d7adca794963d0af1\n\nHere's what I got with <PERSON> 4. Same exact prompt, first try. Nowhere near perfect, but clearly the best of the lot", "score": 55, "created_utc": 1752161223.0}, {"id": "n2dbwq6", "author": "Gold_Bar_4072", "body": "o3 near well made a pancreas lol", "score": 25, "created_utc": 1752157882.0}, {"id": "n2dggzp", "author": "Excellent_Dealer3865", "body": "Somewhat similar result in creative writing comprehension. Grok 4 is way behind other 3 models. Pretty much failed to recognize all the hidden clues.", "score": 17, "created_utc": **********.0}], "commenters": ["freedomheaven", "Excellent_Dealer3865", "metaphorician", "Gold_Bar_4072", "Axelwickm", "vasilenko93"]}, {"success": true, "google_result": {"title": "Would you say Grok 3 is better than GPT 4.5 or Claude 3.7 : r/grok", "url": "https://www.reddit.com/r/grok/comments/1j42134/would_you_say_grok_3_is_better_than_gpt_45_or/", "snippet": "Mar 5, 2025 ... Hello! I'm on SuperGrok too, and I also have access to my company's GPT Pro account and subscribed to <PERSON> just out of curiosity (I' ...", "rank": 5}, "post": {"id": "1j42134", "title": "Would you say Grok 3 is better than GPT 4.5 or Claude 3.7", "author": "VerdantSpecimen", "score": 33, "num_comments": 77, "subreddit": "grok", "url": "https://www.reddit.com/r/grok/comments/1j42134/would_you_say_grok_3_is_better_than_gpt_45_or/", "created_utc": **********.0, "selftext": "Hi!\n\nNow, this is not some tribalistic \"this team vs. team\" post. I currently stopped paying for ChatGPT Plus and I am not paying for Claude PRO. I'm currently only paying for \"SuperGrok\".\n\nCurrently I find that Grok 3 does everything I need, but I'm not super familiar with the abilities of Claude 3.7.  Would you say, objectively, that there's no need to use Claude 3.7 if you're already a Grok 3 suscriber. Or are there for example some specific use-cases where one is clearly better than the other? \n\nI do some creative writing and world-building, but also sometimes solve coding problems, check SQL syntax etc. for work.\n\nThanks!"}, "comments": [{"id": "mg4x98v", "author": "AutoModerator", "body": "Hey u/VerdantSpecimen, welcome to the community! Please make sure your post has an appropriate flair.\n\nJoin our r/Grok Discord server here for any help with API or sharing projects: https://discord.gg/4VXMtaQHk7\n\n\n*I am a bot, and this action was performed automatically. Please [contact the moderators of this subreddit](/message/compose/?to=/r/grok) if you have any questions or concerns.*", "score": 1, "created_utc": **********.0}, {"id": "mg51p11", "author": "Jester347", "body": "Hello! I’m on SuperGrok too, and I also have access to my company’s GPT Pro account and subscribed to Claude Pro just out of curiosity (I’m not going to renew it). So, I’ve had plenty of time to play with different models.\n\nFirst, I want to say that both ChatGPT and Claude are much more polished than Grok. But xAI is working hard on that, and I think they’ll close the gap by the end of April.\n\nWhen it comes to creative writing, Grok 3 ties with GPT-4.5. In my opinion, GPT-4.5 has a fantastic sense of humor, but on the other hand, Grok 3 is better at \"general imagination.\" Grok’s Think mode is superior to o1/o3 in coding and problem-solving. GPT’s DeepResearch provides much more thorough reports than DeepSearch, but it takes 20–30 minutes instead of 1–2 minutes. Finally, Grok 3’s general search function is far better than any other AI I’ve tried.\n\nClaude 3.7 is extremely boring in creative tasks because of <PERSON>throp<PERSON>’s paranoia. It has an amazing Artifacts feature, but xAI is going to add something similar to Grok in the future—even now, you can press \"Preview\" for HTML code and see a very early version of it. Finally, Claude 3.7 still lacks a search function, which drives me nuts.\n\nAnd don’t forget about the limits. Even the cheapest X Premium plan gives you a generous amount of queries for all three Grok modes. GPT-4.5 is now only available on the $200 Pro plan, and word has it that it will be very limited on the Plus plan. As far as I know, on <PERSON> Pro, you can easily burn all your tokens with just 8 big coding queries in 5 hours.\n\nP.S. And don’t forget that xAI is constantly updating the Grok 3 model now. Yesterday, I tried repeating a few of my queries from the first day after the model launched, and the answers were slightly different and, in my opinion, better.", "score": 30, "created_utc": **********.0}, {"id": "mg4yu07", "author": "LeEasy", "body": "Claude 3.7 for coding, <PERSON><PERSON> for the rest. Currently there is no other AI is even close to <PERSON>’s coding capabilities. It can one shot a 3K+ line project without the need of debugging. But that’s it for <PERSON>, it emphasis heavily on coding capabilities, the rest of its capabilities are just meh.", "score": 22, "created_utc": 1741178677.0}, {"id": "mg4z3n6", "author": "RamonDozol", "body": "Due to how much bias there is on all sides, i believe only a blind test can show wich one is perceived to be better by users.", "score": 4, "created_utc": 1741178786.0}, {"id": "mg5e9rc", "author": "Sure_Watercress_6053", "body": "Sonnet 3.7 is amazing for creative writing", "score": 4, "created_utc": 1741184363.0}, {"id": "mg5mmf0", "author": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "body": "The thing about Grok is that they have some stable-ish momentum and you can except solid improvements from them. Also, the direction they are taking with most of things is quite good, e.g. uncensorship, bold moves, free stuff, somewhat open sourcing, actually focusing on features and cool stuff that you would be interested in. And it's basically state-of-the-art in pretty much all regards, even coding (I'm mentioning it because you ask about claude which likes to take pride in good coding).\n\nYou can already find a lot of solid results G<PERSON> has in coding, much better than claude. And the thing is, when you have good whole package (not only coding), then it results in better coding as well, because it usually is all interconnected.\n\nAnthropic are focusing on coding, but they pretty much are shit in a lot of areas, currently one of the most censored models. Zero bold moves, slow progress overall.\n\nLook at top models and how they move forward, some appearing out of nowhere at the same level as <PERSON> or above. Why? Because they don't overthink so many things and move boldly. It is overall wise to look at those things from broader perspective and you made a smart move to switch models", "score": 4, "created_utc": 1741187050.0}], "commenters": ["Sure_Watercress_6053", "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "AutoModerator", "LeEasy", "RamonDozol", "Jester347"]}], "reddit_posts_time": 8.83627963066101, "commenters_history": {"Kathane37": {"ClaudeAI": {"comments": [{"id": "n3a58na", "body": "Yes ? Because they took sonnet 3.5 and post train the hell out of it took create sonnet 4\nThere was no new pretraining of foundational model since a year", "score": 1, "created_utc": 1752595030}, {"id": "n38te1p", "body": "Yes ?\nReasoning model were built on top of existing model using RL\no3 was also built over 4o", "score": 1, "created_utc": 1752579824}, {"id": "n2c5wxx", "body": "Really want to see where all the « AI is a bubble » bros are ?", "score": 7, "created_utc": 1752142237}], "posts": []}}, "OfficeSalamander": {"ClaudeAI": {"comments": [], "posts": []}}, "HighPurrFormer": {"ClaudeAI": {"comments": [], "posts": []}}, "MarinatedTechnician": {"ClaudeAI": {"comments": [], "posts": []}}, "haslo": {"ClaudeAI": {"comments": [], "posts": []}}, "devonschmidt": {"ClaudeAI": {"comments": [{"id": "ls7eqbv", "body": "Smooth transaction from me. Thanks bro!", "score": 1, "created_utc": 1729088843}, {"id": "lmcyhse", "body": "Fucking 1st worlders thinking about themselves, locking out people from other poorer countries with these selfish suggestions.", "score": 1, "created_utc": 1725925839}], "posts": []}}, "Objective_Lab_3182": {"singularity": {"comments": [{"id": "n024d96", "body": "To reach AGI, it's not enough to have resources, intelligence, and follow safety guidelines. It takes courage, ambition, and a hunger for power. <PERSON><PERSON> and <PERSON><PERSON> are two figures who seem to have these es...", "score": -3, "created_utc": 1751029561}, {"id": "mx0c965", "body": "It just can't have <PERSON><PERSON><PERSON> in it.", "score": 3, "created_utc": 1749560323}, {"id": "mv92u3f", "body": "<PERSON><PERSON><PERSON> is a terrible leader. Rigid, pessimistic and just wants to hear from the audience.\n\nThey were surpassed by several companies in the sector. At this rate it will be surpassed even by Amazon, whic...", "score": 5, "created_utc": 1748702524}, {"id": "mf8ixsf", "body": "Yes, it still dominates the mass market. But it's an internal defeat, <PERSON>'s discouragement is notable. \n\nAGI will take a long time, forget 2027. Maybe only in the next decade.", "score": 2, "created_utc": 1740743250}, {"id": "mf8i4oj", "body": "If we went by the proportion of GPUs, yes. But this data is currently unreliable. \n\n OpenAI lost a huge advantage it had, this is an indisputable fact.", "score": 1, "created_utc": 1740742837}, {"id": "mf8h7je", "body": "Twitter has always been toxic because people say what they “think.” And he adopted the policy of freedom of expression (that's what freedom is), which made it even more toxic.\n\nBut that doesn't mean I...", "score": 1, "created_utc": 1740742357}, {"id": "mf8g8zs", "body": "Calling him a radical leftist would be very offensive of me. I don't like hurting people (they are vindictive).", "score": -3, "created_utc": 1740741846}, {"id": "mf8e21s", "body": "If we followed this logic, you would have to stop consuming everything that comes from the industry, as they are all \"evil\". I would have to live as if I were an Amish. \n\nBut if <PERSON><PERSON> supports the libe...", "score": -1, "created_utc": 1740740633}, {"id": "mf8cepq", "body": "Before the topic is deleted and the author banned (the means justify the ends, the liberals' motive is noble, *irony*) I will leave my opinion on the race.\n\nIf xAI managed to reach OpenAIin 2 years, i...", "score": 2, "created_utc": 1740739677}, {"id": "mf8b01e", "body": "Radical liberals who have been brainwashed by the corporate media will come with blood in their eyes on this topic. Be careful, you risk being banned.\n\nIt's very difficult to argue with people who don...", "score": 0, "created_utc": 1740738830}, {"id": "mearg1q", "body": "Ideology is a means to gain power, just like weapons. Truth is a timeless power, it overcomes time.", "score": 1, "created_utc": 1740289852}, {"id": "mear6qr", "body": "Power = Lie, steal, kill, blackmail, deny reality.\n\nThese individuals are powerful, and are capable of anything. But there is no vacuum in power, other individuals would occupy that power. And no, pow...", "score": 1, "created_utc": 1740289726}, {"id": "meapqzn", "body": "The truth does not beat a gun physically. But in the long run, it destroys its enemies.", "score": 2, "created_utc": 1740289043}, {"id": "meap0ie", "body": "He is not the only individual with this power. There is no saint in this story. Calling a product bad because its creator has power is extremely stupid.", "score": 1, "created_utc": 1740288695}, {"id": "meanspv", "body": "Those who seek power only want power. This type of individual will use any means to achieve this goal. He will lie, steal, blackmail, distort reality and even kill.\n\nEvery politician hates the truth, ...", "score": 1, "created_utc": 1740288140}, {"id": "meamn9k", "body": "The political activist is toxic. It vibrates at a very low frequency. He has a sad life.", "score": 8, "created_utc": 1740287616}, {"id": "mealdls", "body": "The fanatical militant hates the truth. Because the truth goes against those who seek power.\nThat's why I stay away from politics, because I only see liars making speeches and brainless activists.", "score": -1, "created_utc": 1740287047}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON>on Musk is the terror of liberals. They cannot separate technology and politics. They should ban fanatical liberals from here, clean it up.", "score": -6, "created_utc": 1740286688}, {"id": "me5uxev", "body": "Do you believe <PERSON><PERSON> is? I see him as more adept at transhumanism.", "score": 0, "created_utc": 1740231799}, {"id": "me5t9l7", "body": "Personal experience and skepticism is the best way. Blindly believing in authority or something that is consensual is risky. \n\nBasic example: The corporate media and the majority of the scientific cla...", "score": 0, "created_utc": 1740231187}], "posts": []}}, "Cagnazzo82": {"singularity": {"comments": [{"id": "n3xehce", "body": "The mindset still persists that \"if we just take down OpenAI then AI will go away and we'll be magically back in 2019.\"\n\nThat boat has sailed. And these people making these videos or crafting hit piec...", "score": 7, "created_utc": 1752891306}, {"id": "n3xe4at", "body": "Fast-forward less than 1 month later and <PERSON><PERSON> introduces the idea of being closed source (for safety concerns) to <PERSON>, <PERSON>, and <PERSON><PERSON>.\n\nhttps://preview.redd.it/no0va3f5pqdf1.jpeg?width=1179&format=pjp...", "score": 3, "created_utc": 1752891167}, {"id": "n3vut0b", "body": "$200m is a drop in the bucket in terms of AI investment though.\n\nThey'll have to weigh whether sabotaging their models is worth it.", "score": 7, "created_utc": 1752871949}, {"id": "n3uzx9r", "body": "So there's a purpose. That changes things.", "score": 2, "created_utc": **********}, {"id": "n3uya3j", "body": "Correct me if I'm wrong, but wasn't the purpose of the original ARC-AGI supposed to determine when we've achieved AGI by whether or not models could pass the test?\n\nIt was supposed to be a benchmark t...", "score": -3, "created_utc": **********}, {"id": "n3uxd2q", "body": "Ultimately what purpose do they serve if their benchmarks are saturated 🤷", "score": 3, "created_utc": **********}, {"id": "n3uq37e", "body": "The entire world reports on US politics, science, health, geopolitics different than the domestic US right-wing media does.\n\nIs the whole planet earth considered woke? \n\nAnd what are the models suppos...", "score": 21, "created_utc": **********}, {"id": "n3uowsc", "body": "It is definitely not how the federal government does business.\n\nIt's how <PERSON>'s administration does business.\n\nThe federal government funded the development of the internet, GPS, etc and didn'...", "score": 8, "created_utc": **********}, {"id": "n3pwjr2", "body": "The administration saw <PERSON><PERSON><PERSON><PERSON> was possible and salivated at turning all properly aligned models into <PERSON><PERSON><PERSON><PERSON>.\n\nEdit: And it's funny to see they fought against states regulating AI and now th...", "score": 9, "created_utc": **********}, {"id": "n3pvjq6", "body": "Neutral = all AI models being <PERSON><PERSON><PERSON><PERSON>.", "score": 5, "created_utc": **********}, {"id": "n3pjqlq", "body": "And the media barely covers it...\n\n...while the media absolutely lost their sh\\*... over OpenAI giving the Sky voice personality.", "score": 13, "created_utc": 1752788892}, {"id": "n3pa98w", "body": "But <PERSON><PERSON> said 'first reasoning principles...'\n\nHe said the magic words.", "score": 3, "created_utc": 1752786076}, {"id": "n3p9vzh", "body": "Buta but but... anime WaiFu 😵‍💫\n\n- typical X cope", "score": 1, "created_utc": 1752785971}, {"id": "n3kk3mx", "body": "$14.3 billion bought the 'loyalty' of Scale AI's founders...\n\n...and they immediately turned around and fired 200 staffers from the team responsible for making the company valuable enough for Meta to ...", "score": 0, "created_utc": 1752724414}, {"id": "n3k7w8x", "body": "Please zoom the camera back a bit from his reptilian eyes.", "score": 3, "created_utc": 1752719517}, {"id": "n3jvfz7", "body": "His thumbnails are cringe af, but his videos are very level-headed and informative.\n\n<PERSON> would have dictated otherwise, but he's not one of the bad guys.", "score": 2, "created_utc": 1752714991}, {"id": "n3gvmuz", "body": "This isn't about 'do what you like'. But rather calling out that a culture based on people paid lottery winnings isn't sustainable.\n\nThose people taking the checks are for sure not loyal to <PERSON><PERSON>. And ...", "score": 62, "created_utc": 1752683262}, {"id": "n34y25z", "body": "Same award went to all frontier labs.\n\nI agree with the consternation however that some of these labs are not ready... for a myriad of reasons.", "score": 8, "created_utc": 1752523626}, {"id": "n34gxom", "body": "What the heck...", "score": 1, "created_utc": 1752518688}, {"id": "n2zwfp5", "body": "BusinessInsider has been hating and hating, article after article. It's been the end of OpenAI for almost 2 years now.\n\nNevertheless OpenAI is still chugging along, sitting at #1.\n\nSuffice to say at l...", "score": 118, "created_utc": 1752454800}], "posts": []}}, "focal-fossa": {"singularity": {"comments": [{"id": "m1ehc9b", "body": "I'm integrating APIs. Can't tell about their websites and apps.  \n  \nComparing to GPT-4o API:  \n\\- Llama 3 is perfect for real-time data and it also provides sources.   \n\\- xAI Grok Beta has less cens...", "score": 1, "created_utc": 1733857433}], "posts": []}}, "Its_not_a_tumor": {"singularity": {"comments": [{"id": "n37owd8", "body": "I looked for the full interview online. Looks like the audio didn't work and they cut it off, oof awkward.", "score": 2, "created_utc": 1752558164}, {"id": "n2akbpi", "body": "It will be SOTA at maybe two benchmarks that they will focus on talking about, while the rest of the benchmarks be good but not the best. Will be useful for very specific use cases but will be oversha...", "score": 1, "created_utc": 1752114103}, {"id": "myah3hh", "body": "Sycophancy\\*cough.", "score": 6, "created_utc": 1750178496}, {"id": "mwqia1q", "body": "I think you misunderstood the meme dude.", "score": 1, "created_utc": 1749422998}, {"id": "mwor6x4", "body": "Soup was <PERSON> I think", "score": 2, "created_utc": 1749403267}, {"id": "mwdq7zm", "body": "4.1 has a context window of 1M", "score": 10, "created_utc": 1749242491}, {"id": "mwdlims", "body": "I believe it was copywrite related. Their argument is, if ChatGPT responds with something that is protected by copywrite, it could hypothetically be covered up after if the chat history isn't retained...", "score": 1, "created_utc": 1749241060}], "posts": []}}, "Jean-Porte": {"singularity": {"comments": [], "posts": []}}, "xAragon_": {"ClaudeAI": {"comments": [], "posts": []}}, "Sad_Run_9798": {"ClaudeAI": {"comments": [], "posts": []}}, "Chogo82": {"ClaudeAI": {"comments": [], "posts": []}}, "Heavy_Hunt7860": {"ClaudeAI": {"comments": [{"id": "n2lqy3j", "body": "Sh$t, I better commit to git. It’s been 12 hours for me. \n\nHave had it hijack my code in the past too.", "score": 1, "created_utc": 1752261860}, {"id": "my5r2n1", "body": "This is world-class feedback!", "score": 2, "created_utc": 1750111586}], "posts": []}}, "MrBietola": {"ClaudeAI": {"comments": [{"id": "n0nebxb", "body": "3.5 was far superior. 4 sonnett is just dumb. to match 2 dataframes it was using two inner  for cycles. i would use Opus, but the rate limits are killing me", "score": 1, "created_utc": 1751318698}], "posts": []}}, "freedomheaven": {"singularity": {"comments": [{"id": "n3a780m", "body": "Correction: Grok 4 ranks at number 3.", "score": 13, "created_utc": 1752595581}, {"id": "n2dg2sr", "body": "Looks like Gemini 2.5 is clear winner here.", "score": 204, "created_utc": 1752159074}, {"id": "n2blrtp", "body": "Uncertainty on GPT-5 release. Also, it takes a while for lmarena dashboard to get updated", "score": 30, "created_utc": 1752130807}, {"id": "n2bjde0", "body": "Yes it is. Its almost a certainty that Grok 4 will overtake Gemini 2.5 pro but still around 2.8 times more people are betting that Google has something already ready to ship that can crush it.", "score": 26, "created_utc": 1752129519}, {"id": "mya9m9q", "body": "Announcement link: [https://blog.google/products/gemini/gemini-2-5-model-family-expands/](https://blog.google/products/gemini/gemini-2-5-model-family-expands/)\n\nBenchmarks:\n\nhttps://i.redd.it/lax1j09x...", "score": 87, "created_utc": 1750176374}], "posts": []}}, "Excellent_Dealer3865": {"singularity": {"comments": [{"id": "n2zoieu", "body": "Considering how unproportionable high was grok3 this one will be top 1 for sure. Musk will 100% hire ppl to rank it up", "score": 2, "created_utc": 1752451967}, {"id": "n2j7gqw", "body": "Yeah, it indeed keeps the context and can 'recall' specifics. If it can't comprehend specifics what's value of it recalling them?\n\nIf you give it a clue that you, I dunno, a vampire, but it completely...", "score": 1, "created_utc": 1752234488}, {"id": "n2gizmz", "body": "It's an amount of tokens a model could hold in its memory without losing context which has nothing to do with intelligence.", "score": 1, "created_utc": 1752191379}, {"id": "n2f6kg4", "body": "hat seems normal. They'll all get to a point where an average person won't be able to take advantage.\n\nI dunno, I expect a model that is 'twice as good as any other top tier model' to be significantly...", "score": 0, "created_utc": 1752176552}, {"id": "n2ekb3u", "body": "I wish that's the truth. I tested it myself and it doesn't correlate. Considering the fact how many times <PERSON><PERSON> blatantly lied about his achievements / results - I of course would have a bias. Yet toda...", "score": 4, "created_utc": 1752170211}, {"id": "n2dm87d", "body": "Well, I don't use AI for SVG/Coding - it's mostly for my day to day activities, formula readings for various chemicals and so on + creative writing. And for all the complex and highly nuanced sci-fi /...", "score": 1, "created_utc": 1752160790}, {"id": "n2dggzp", "body": "Somewhat similar result in creative writing comprehension. Grok 4 is way behind other 3 models. Pretty much failed to recognize all the hidden clues.", "score": 17, "created_utc": **********}, {"id": "n2dfcc0", "body": "Because it's fake numbers. Somehow musk bought or got access to evaluation data for the tests and fully fabricated it. Anyone with an access to an openrouter or any other source of Grok will find it o...", "score": 2, "created_utc": 1752158866}, {"id": "n2ddq6a", "body": "Tried it for about 50\\~ prompts via openrouter. If there is ANY difference in intelligence level between grok 3 and grok 4 - it's very subtle. I can't notice it. Still a vast intelligence gap between ...", "score": 2, "created_utc": 1752158406}, {"id": "n2dcfhp", "body": "You're just plain lying to people in order to promote your AI. It's a combination of default benchmarks that have a combined index that they use for their own index. I guess it works well for MAGA but...", "score": 3, "created_utc": 1752158034}, {"id": "n2dbbma", "body": "Tried <PERSON> 4 (regular thinking) for creative writing understanding / nuance comprehension - seems worse than sonnet, 2.5 pro and o3. I did quite a lot of attempts. Very unimpressive so far.\n\nThey eith...", "score": 1, "created_utc": 1752157712}, {"id": "n1m2hja", "body": "I personally became much more critical now that I have o3 and gemini pro. I question all the formulas on all cosmetic / dermatology products, question food and its suitability, question pretty much ev...", "score": 1, "created_utc": 1751797222}, {"id": "mx4n5n4", "body": "Yes, please.", "score": 2, "created_utc": 1749607148}, {"id": "mx4n4u1", "body": "Yes, please.", "score": 2, "created_utc": 1749607141}, {"id": "mx36l0k", "body": "Can't wait for my superintelligent femboy. Wen", "score": 7, "created_utc": 1749589704}], "posts": []}}, "metaphorician": {"singularity": {"comments": [{"id": "n2dnsz1", "body": "https://preview.redd.it/oxirbtkqe2cf1.png?width=1164&format=png&auto=webp&s=53f58638f5d434a89183966d7adca794963d0af1\n\nHere's what I got with <PERSON> 4. Same exact prompt, first try. Nowhere near p...", "score": 52, "created_utc": 1752161223}, {"id": "ma2760c", "body": "Democratic values like the US supported Gaza genocide, <PERSON>? Democratic values like the US displacing 38 million people in the made up war on terror, <PERSON>? Take a long hard look at the track record ...", "score": 1, "created_utc": 1738264540}, {"id": "m9xigy3", "body": "I wonder what an old Roman augur would make of these peculiar murmurations", "score": 182, "created_utc": 1738199431}, {"id": "m9ji0rh", "body": "https://preview.redd.it/sx6yhqppcmfe1.png?width=2342&format=png&auto=webp&s=4e68ea321f4ecc0abf16155723b3438a88da58b4\n\nIf you select the \"search term\" chatgpt instead of the \"software\", deepseek has pa...", "score": 3, "created_utc": 1738019710}], "posts": []}}, "Gold_Bar_4072": {"singularity": {"comments": [{"id": "n3royny", "body": "Can it diagnose and find solutions to diseases better than humans?,can it identify images better or faster than humans?,can it solve arc agi 2 better than humans?(60%)and most importantly,can It clean...", "score": 0, "created_utc": 1752818014}, {"id": "n3ic5iu", "body": "They will not release it until their traffic is affected by other frontierlabs lol", "score": 2, "created_utc": 1752697798}, {"id": "n348i15", "body": "Grok API is only 1.5x of gemini's,yet 3x times more expensive 🤣,shit loads of tokens", "score": 8, "created_utc": 1752516291}, {"id": "n2jtig5", "body": "June 2018 - gpt 1\n\n\nAfter 17 months, November 5, 2019 - gpt 2\n\n\nAfter 7 months, June 11, 2020 - gpt 3\n\n\nAfter 30 months, November 30, 2022 - gpt 3.5\n\n\nAfter 3 months, 14 March 2023 - gpt 4\n\n\nAfter 24 ...", "score": 4, "created_utc": 1752242092}, {"id": "n2dbwq6", "body": "o3 near well made a pancreas lol", "score": 24, "created_utc": 1752157882}, {"id": "n2bckos", "body": "Wow,<PERSON><PERSON> still works,imagine stargate with 400k blackwells 🤯", "score": 7, "created_utc": 1752125980}, {"id": "n2bcera", "body": "Post deleted 💀", "score": 1, "created_utc": 1752125896}, {"id": "n1s35a0", "body": "Nah,the benchmark scores are completely different for deep think", "score": 6, "created_utc": 1751880652}, {"id": "n0dotei", "body": "Bigger people than deal realizes", "score": 1, "created_utc": 1751187697}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "body": "They reuploaded...the same models", "score": 13, "created_utc": 1750178868}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "body": "EXACTLY", "score": 2, "created_utc": 1750178741}, {"id": "mwezw2c", "body": "It is remarkable", "score": 0, "created_utc": 1749258273}, {"id": "mw5r3e1", "body": "Same/better scores than o3 at 1/4th its cost, coogle gooked", "score": 97, "created_utc": 1749140748}, {"id": "mone154", "body": "Still can't believe they re offering the best reasoning model on earth for 1 million context", "score": 86, "created_utc": 1745431874}, {"id": "mnnjdye", "body": "I think it's great considering all the improvement it has over 2.0", "score": 9, "created_utc": 1744924055}, {"id": "mnfdwbi", "body": "O3 - 3 sides (triangle) o4- 4 sides (square)", "score": 11, "created_utc": 1744818565}, {"id": "mneux7s", "body": "Very high in both performance and cost lol", "score": 5, "created_utc": 1744812917}, {"id": "mneu2sk", "body": "I remember a single prompt cost around 3k $ at high compute? How are they releasing this", "score": 64, "created_utc": 1744812655}, {"id": "mddzouq", "body": "Unnecessary hate even before the release lol,grok team cooked", "score": 3, "created_utc": 1739859312}, {"id": "mcfflay", "body": "If 4.5 will be as powerful as deepseek, it will be used a lot worldwide ", "score": -6, "created_utc": 1739392173}], "posts": []}}, "Axelwickm": {"singularity": {"comments": [{"id": "n2dpogv", "body": "Makes me curious how well it would do if you threatened it family.", "score": 4, "created_utc": 1752161751}, {"id": "n2df493", "body": "Grok 4 certainly is not strong here", "score": 71, "created_utc": 1752158804}, {"id": "mvypvnv", "body": "There are 100 trillions years until the last stars will stop forming, and we only made it 14 billion years so far. ", "score": 1, "created_utc": 1749049823}, {"id": "mvypnu0", "body": "Agree. AI will take over and prevent all future consciousness to take form, so the anthropological principle applies. We exist now because until now is the only time we could exist, in the same way as...", "score": 1, "created_utc": 1749049760}], "posts": []}}, "vasilenko93": {"singularity": {"comments": [{"id": "n3ogavp", "body": "Well the agent showed off by OpenAI today isn’t useful. It’s too slow. It will take a few more iterations for it to become useful. By the time those iterations happen Grok 5 will come out with most li...", "score": 1, "created_utc": 1752777536}, {"id": "n3oapul", "body": "Did we watch the same livestream? It took forever to do basic things.", "score": 6, "created_utc": 1752775936}, {"id": "n3o7kxu", "body": "They called it a new model multiple times in the livestream", "score": 5, "created_utc": 1752775041}, {"id": "n3o5l01", "body": "You are unhinged now.", "score": -1, "created_utc": 1752774493}, {"id": "n3o5ahi", "body": "Yeah but Grok 3 came out after GPT4o and now Grok 4 is out. Where is GPT 5? Also in the livestream they said this is a new model.\n\nThe point is Grok appears to be improving at a significantly faster r...", "score": 5, "created_utc": 1752774415}, {"id": "n3o4smq", "body": "Wait. What? That’s it? Grok 4 had access to less tools and scored higher (Grok doesn’t have browser and computer, just terminal with ability to write and execute code). Man OpenAI is behind. GPT-5 bet...", "score": 4, "created_utc": 1752774282}, {"id": "n3o346a", "body": "No, your example was how easily someone can tamper with over the counter medications.\n\nThe better question is how easily is it to acquire the materials? You don’t need AI to look up deadly poisons and...", "score": 1, "created_utc": 1752773830}, {"id": "n3nupsv", "body": "Information should never be suppressed. Period.", "score": -2, "created_utc": 1752771569}, {"id": "n3ntjib", "body": "What does that prove?", "score": 1, "created_utc": 1752771253}, {"id": "n3ngt7t", "body": "Of all the safety concerns, this one is not very important. Who cares if someone has info on how to build a bomb. It means nothing.\n\nThe physical ability to build a bomb is what’s important. The knowl...", "score": 0, "created_utc": 1752767728}, {"id": "n34lwx7", "body": "Did they try Grok 4 with reasoning or base? How about Grok 4 Heavy?\n\nHonestly the variations of each model is getting annoying", "score": 2, "created_utc": 1752520131}, {"id": "n314vzm", "body": "Where is xAI in this? Elon bought a lot of GPUs", "score": 3, "created_utc": 1752473380}, {"id": "n2y7p96", "body": "Coding is more puzzle solving", "score": 4, "created_utc": 1752435160}, {"id": "n2y7kq4", "body": "Do you know there definition of “multimodal?”", "score": 5, "created_utc": 1752435122}, {"id": "n2y7gex", "body": "Clearly not over fitting on coding and multi modality benchmarks", "score": 3, "created_utc": 1752435086}, {"id": "n2y7bi6", "body": "Scoring so high on humanity’s last exam is half baked? If that’s half baked than full baked is basically AGI", "score": 23, "created_utc": 1752435045}, {"id": "n2xuplr", "body": ">especially coding\n\nMan it’s almost as if nobody watched the livestream. <PERSON><PERSON> said the focus of this release was reasoning and math and science. That’s why they showed off mostly math benchmarks and H...", "score": 56, "created_utc": 1752431251}, {"id": "n2oloig", "body": "Elon burning cash to reach AGI is dope.", "score": 1, "created_utc": 1752298820}, {"id": "n2nqjbo", "body": "<PERSON> just pulled a Elon Musk", "score": 5, "created_utc": 1752285561}, {"id": "n2mik4m", "body": "Elon dumped so much money on GPUs. Grok 5 will saturate all benchmarks", "score": 8, "created_utc": 1752270179}], "posts": []}}, "Sure_Watercress_6053": {"grok": {"comments": [], "posts": []}}, "adrian-dartagnan": {"grok": {"comments": [{"id": "mxxr6q2", "body": "I don't see it in Poland. Is it available for free users too?", "score": 1, "created_utc": 1750007340}], "posts": []}}, "AutoModerator": {"grok": {"comments": [], "posts": []}}, "LeEasy": {"grok": {"comments": [{"id": "mji2ztl", "body": "I think that function is only available for the $50/Month sub tier.", "score": 1, "created_utc": 1742833946}, {"id": "mg4z8xf", "body": "Images only works with “think” mode off I believe. It’s either “Think mode” doesn’t have native image processing capability, or there is a bug in image processing pipeline (it processes and answers yo...", "score": 1, "created_utc": 1741178848}, {"id": "mg4yz1w", "body": "Yes, but it resets every 2 hours instead of every week.", "score": 0, "created_utc": 1741178734}, {"id": "mg4yu07", "body": "<PERSON> 3.7 for coding, <PERSON><PERSON> for the rest. Currently there is no other AI is even close to <PERSON>’s coding capabilities. It can one shot a 3K+ line project without the need of debugging. But that’s it ...", "score": 21, "created_utc": 1741178677}], "posts": []}}, "RamonDozol": {"grok": {"comments": [], "posts": []}}, "Jester347": {"grok": {"comments": [{"id": "mltn30n", "body": "Presumably only SuperGrok has 128K context window. Free tier has lesser.", "score": 2, "created_utc": 1744006700}, {"id": "mjh6j3p", "body": "xAI is working on a memory function right now. Until it’s released, you can use a single megathread for all your conversations. Grok has a 128K context window and is very good at handling long convers...", "score": 3, "created_utc": **********}, {"id": "mjgbvam", "body": "I have no clue why they don’t release it to a broader audience. I’m on the Android beta, and over the last two weeks, the app has improved significantly. They’ve added Think and DeepSearch functionali...", "score": 6, "created_utc": **********}, {"id": "mj<PERSON><PERSON>", "body": "Sorry for going a bit off-topic, but how can I get that $25 price? I have an X Premium account, and I also pay $30 for SuperGrok using a different email. Do I need to log in via X to get the discount?...", "score": 1, "created_utc": **********}, {"id": "mj77i8l", "body": "I suggest that you stay on SuperGrok. On par with GPT-4.5, these are the two best real-time models available now. However, GPT-4.5 offers only 50 queries per week, while SuperGrok provides 100 queries...", "score": 16, "created_utc": **********}, {"id": "mj697j0", "body": "<PERSON> is a good coder, but as far as I know, it has relatively strict limits, even for Plus subscribers. I advise you to be patient—I’m sure that xAI will resolve the main issues in just 1-2 months.", "score": 1, "created_utc": **********}, {"id": "mj5uyqw", "body": "That's on the server side. Wait until they fix it or add more servers. Unfortunately, Grok 3 is still in beta, and the software and infrastructure are far worse than the model itself.", "score": 3, "created_utc": **********}, {"id": "mi<PERSON><PERSON><PERSON><PERSON>", "body": "Chat memory is in development and already available for testers. Just wait a few weeks.", "score": 5, "created_utc": **********}, {"id": "mi2zm18", "body": "I’ve seen that kind of behavior in every LLM I’ve tried. Reasoning models perform slightly better, but at the cost of longer response times. I think this happens because of the randomization that lies...", "score": 1, "created_utc": **********}, {"id": "mht7wfg", "body": "I'm m 100% sure that feature will be added. Having a developing memory for each user is one of the main requirements to achieve AGI. However, this feature isn’t easy to implement, and in ChatGPT, the ...", "score": 1, "created_utc": 1741984899}, {"id": "mhpghz9", "body": "I’ll add my voice to the request to publish the prompt.", "score": 2, "created_utc": 1741933848}, {"id": "mhn1ix0", "body": "Because ChatGPT is like Kleenex in the AI world, many people use it without even knowing there are plenty of other AIs on the market. Some use DeepSeek because it generated a lot of buzz and it’s free...", "score": 26, "created_utc": 1741901602}, {"id": "mhlhqo3", "body": "Right now, there are two best models for chatting: Grok 3 and GPT-4.5. Both were trained on supercomputers with around 100,000 chips. And I wonder how it would feel to chat with a model trained on 1,0...", "score": 2, "created_utc": 1741885869}, {"id": "mh722yc", "body": "For me, <PERSON><PERSON> and DeepSeek R1 are the best roleplaying models. <PERSON><PERSON> writes very deep texts but is often slow-paced. R1 starts forgetting the rules after 20-30 responses and goes completely wild. Someti...", "score": 7, "created_utc": 1741698032}, {"id": "mh65qta", "body": "I asked Grok 3 the same question:\n\n17. Sega Rally 2 - Not a traditional fighter, but included here due to occasional misclassification; it’s a racing game.", "score": 3, "created_utc": 1741681361}, {"id": "mgwe57y", "body": "That was on the news. Microsoft Copilot uses 4o for basic queries and started using o3-mini-high for the 'Think Deeper' option a few days ago. Before that, they used o1, but in my experience, o3-mini-...", "score": 5, "created_utc": 1741547971}, {"id": "mgol6iy", "body": "I'm on X Premium and my limit also decreased from 50 to 25 on Grok.com. I still have 50 queries on X but don't want use it for Grok.", "score": 6, "created_utc": 1741441921}, {"id": "mgiatbq", "body": "Why not try it yourself? Grok 3's free plan offers 20 basic queries every 2 hours, 10 think queries every 24 hours, and 10 DeepSearch queries every 24 hours. I believe that should be enough for a firs...", "score": 8, "created_utc": **********}, {"id": "mg7aq1m", "body": "Maybe it’s superior, but 50 queries a week for the Plus subscription seems like nonsense. But if you earn for a living by writing the best option is to invest in both, of course.", "score": 2, "created_utc": **********}, {"id": "mg51p11", "body": "Hello! I’m on SuperGrok too, and I also have access to my company’s GPT Pro account and subscribed to Claude Pro just out of curiosity (I’m not going to renew it). So, I’ve had plenty of time to play ...", "score": 31, "created_utc": **********}], "posts": []}}}, "commenters_history_time": 4.****************, "total_time": 14.***************, "success": true, "error_message": ""}