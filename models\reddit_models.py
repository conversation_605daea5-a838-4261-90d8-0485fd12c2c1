"""
CogBridges Search - Reddit数据模型
定义Reddit帖子、评论、用户等数据结构
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import json


@dataclass
class RedditComment:
    """Reddit评论模型"""
    id: str
    body: str
    author: str
    score: int
    created_utc: float
    parent_id: str
    subreddit: str
    permalink: str
    is_submitter: bool = False
    gilded: int = 0
    replies_count: int = 0

    # 新增上下文信息字段
    submission_id: str = ""  # 所属帖子ID
    submission_title: str = ""  # 所属帖子标题
    submission_url: str = ""  # 所属帖子URL
    parent_comment_id: str = ""  # 父评论ID（如果是回复评论）
    parent_comment_author: str = ""  # 父评论作者
    parent_comment_body: str = ""  # 父评论内容（截取）
    comment_depth: int = 0  # 评论层级深度
    context_summary: str = ""  # 上下文摘要
    
    @property
    def created_datetime(self) -> datetime:
        """获取创建时间的datetime对象"""
        return datetime.fromtimestamp(self.created_utc)
    
    @property
    def url(self) -> str:
        """获取完整URL"""
        return f"https://reddit.com{self.permalink}"
    
    @property
    def is_top_level(self) -> bool:
        """检查是否为一级评论（直接回复帖子）"""
        return self.parent_id.startswith('t3_')
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "body": self.body,
            "author": self.author,
            "score": self.score,
            "created_utc": self.created_utc,
            "created_datetime": self.created_datetime.isoformat(),
            "parent_id": self.parent_id,
            "subreddit": self.subreddit,
            "permalink": self.permalink,
            "url": self.url,
            "is_submitter": self.is_submitter,
            "is_top_level": self.is_top_level,
            "gilded": self.gilded,
            "replies_count": self.replies_count,
            # 新增上下文信息
            "submission_id": self.submission_id,
            "submission_title": self.submission_title,
            "submission_url": self.submission_url,
            "parent_comment_id": self.parent_comment_id,
            "parent_comment_author": self.parent_comment_author,
            "parent_comment_body": self.parent_comment_body,
            "comment_depth": self.comment_depth,
            "context_summary": self.context_summary
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RedditComment':
        """从字典创建实例"""
        # 过滤掉计算属性
        filtered_data = {k: v for k, v in data.items() 
                        if k in cls.__dataclass_fields__}
        return cls(**filtered_data)


@dataclass
class RedditPost:
    """Reddit帖子模型"""
    id: str
    title: str
    selftext: str
    author: str
    score: int
    upvote_ratio: float
    num_comments: int
    created_utc: float
    subreddit: str
    permalink: str
    url: str
    is_self: bool = True
    gilded: int = 0
    pinned: bool = False
    locked: bool = False
    archived: bool = False
    over_18: bool = False
    
    @property
    def created_datetime(self) -> datetime:
        """获取创建时间的datetime对象"""
        return datetime.fromtimestamp(self.created_utc)
    
    @property
    def reddit_url(self) -> str:
        """获取Reddit完整URL"""
        return f"https://reddit.com{self.permalink}"
    
    @property
    def content_length(self) -> int:
        """获取内容长度"""
        return len(self.selftext)
    
    @property
    def engagement_score(self) -> float:
        """计算参与度分数"""
        if self.score <= 0:
            return 0.0
        
        # 基于分数、评论数和点赞比例计算参与度
        base_score = self.score * self.upvote_ratio
        comment_bonus = min(self.num_comments * 0.1, 50)  # 评论数奖励，最多50分
        
        return base_score + comment_bonus
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "selftext": self.selftext,
            "author": self.author,
            "score": self.score,
            "upvote_ratio": self.upvote_ratio,
            "num_comments": self.num_comments,
            "created_utc": self.created_utc,
            "created_datetime": self.created_datetime.isoformat(),
            "subreddit": self.subreddit,
            "permalink": self.permalink,
            "url": self.url,
            "reddit_url": self.reddit_url,
            "is_self": self.is_self,
            "gilded": self.gilded,
            "pinned": self.pinned,
            "locked": self.locked,
            "archived": self.archived,
            "over_18": self.over_18,
            "content_length": self.content_length,
            "engagement_score": self.engagement_score
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RedditPost':
        """从字典创建实例"""
        # 过滤掉计算属性
        filtered_data = {k: v for k, v in data.items() 
                        if k in cls.__dataclass_fields__}
        return cls(**filtered_data)


@dataclass
class RedditUser:
    """Reddit用户模型"""
    username: str
    created_utc: float
    comment_karma: int = 0
    link_karma: int = 0
    is_gold: bool = False
    is_mod: bool = False
    has_verified_email: bool = False
    
    @property
    def created_datetime(self) -> datetime:
        """获取账户创建时间的datetime对象"""
        return datetime.fromtimestamp(self.created_utc)
    
    @property
    def total_karma(self) -> int:
        """获取总karma"""
        return self.comment_karma + self.link_karma
    
    @property
    def account_age_days(self) -> int:
        """获取账户年龄（天数）"""
        return (datetime.now() - self.created_datetime).days
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "username": self.username,
            "created_utc": self.created_utc,
            "created_datetime": self.created_datetime.isoformat(),
            "comment_karma": self.comment_karma,
            "link_karma": self.link_karma,
            "total_karma": self.total_karma,
            "account_age_days": self.account_age_days,
            "is_gold": self.is_gold,
            "is_mod": self.is_mod,
            "has_verified_email": self.has_verified_email
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RedditUser':
        """从字典创建实例"""
        # 过滤掉计算属性
        filtered_data = {k: v for k, v in data.items() 
                        if k in cls.__dataclass_fields__}
        return cls(**filtered_data)


@dataclass
class UserHistory:
    """用户历史数据模型"""
    user: RedditUser
    posts: List[RedditPost] = field(default_factory=list)
    comments: List[RedditComment] = field(default_factory=list)
    subreddit_activity: Dict[str, int] = field(default_factory=dict)
    
    @property
    def total_posts(self) -> int:
        """获取总帖子数"""
        return len(self.posts)
    
    @property
    def total_comments(self) -> int:
        """获取总评论数"""
        return len(self.comments)
    
    @property
    def most_active_subreddit(self) -> Optional[str]:
        """获取最活跃的子版块"""
        if not self.subreddit_activity:
            return None
        return max(self.subreddit_activity, key=self.subreddit_activity.get)
    
    @property
    def average_post_score(self) -> float:
        """获取平均帖子分数"""
        if not self.posts:
            return 0.0
        return sum(post.score for post in self.posts) / len(self.posts)
    
    @property
    def average_comment_score(self) -> float:
        """获取平均评论分数"""
        if not self.comments:
            return 0.0
        return sum(comment.score for comment in self.comments) / len(self.comments)
    
    def get_subreddit_posts(self, subreddit: str) -> List[RedditPost]:
        """获取指定子版块的帖子"""
        return [post for post in self.posts if post.subreddit.lower() == subreddit.lower()]
    
    def get_subreddit_comments(self, subreddit: str) -> List[RedditComment]:
        """获取指定子版块的评论"""
        return [comment for comment in self.comments if comment.subreddit.lower() == subreddit.lower()]
    
    def get_top_posts(self, limit: int = 10) -> List[RedditPost]:
        """获取最高分的帖子"""
        return sorted(self.posts, key=lambda x: x.score, reverse=True)[:limit]
    
    def get_top_comments(self, limit: int = 20) -> List[RedditComment]:
        """获取最高分的评论"""
        return sorted(self.comments, key=lambda x: x.score, reverse=True)[:limit]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "user": self.user.to_dict(),
            "posts": [post.to_dict() for post in self.posts],
            "comments": [comment.to_dict() for comment in self.comments],
            "subreddit_activity": self.subreddit_activity,
            "statistics": {
                "total_posts": self.total_posts,
                "total_comments": self.total_comments,
                "most_active_subreddit": self.most_active_subreddit,
                "average_post_score": self.average_post_score,
                "average_comment_score": self.average_comment_score
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserHistory':
        """从字典创建实例"""
        user = RedditUser.from_dict(data['user'])
        posts = [RedditPost.from_dict(post_data) for post_data in data.get('posts', [])]
        comments = [RedditComment.from_dict(comment_data) for comment_data in data.get('comments', [])]
        subreddit_activity = data.get('subreddit_activity', {})
        
        return cls(
            user=user,
            posts=posts,
            comments=comments,
            subreddit_activity=subreddit_activity
        )
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'UserHistory':
        """从JSON字符串创建实例"""
        data = json.loads(json_str)
        return cls.from_dict(data)
