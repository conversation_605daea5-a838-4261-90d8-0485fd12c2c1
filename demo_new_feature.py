"""
演示新的子版块分类功能
"""

import asyncio
import os
import json

# 设置环境变量
os.environ["REPLICATE_API_TOKEN"] = "****************************************"

from services.cogbridges_service import CogBridgesService


async def demo_new_search_feature():
    """演示新的搜索功能"""
    print("=== CogBridges 子版块分类功能演示 ===\n")
    
    # 初始化服务
    cogbridges_service = CogBridgesService()
    
    # 测试查询
    query = "Should I subscribe to GPT, <PERSON>, <PERSON>rok, or Gemini?"
    
    print(f"搜索查询: {query}")
    print("=" * 50)
    
    try:
        # 使用新的带分类功能的搜索
        print("🚀 开始执行带子版块分类的搜索...")
        result = await cogbridges_service.search_with_subreddit_classification(query)
        
        if result.success:
            print("✅ 搜索成功完成!\n")
            
            # 显示基本信息
            print(f"📊 搜索统计:")
            print(f"   总耗时: {result.total_time:.2f} 秒")
            print(f"   Google搜索: {result.google_search_time:.2f} 秒")
            print(f"   Reddit帖子: {result.reddit_posts_time:.2f} 秒")
            print(f"   用户历史: {result.commenters_history_time:.2f} 秒")
            print()
            
            # 显示Google搜索结果
            print(f"🔍 Google搜索结果 ({len(result.google_results)} 个):")
            for i, google_result in enumerate(result.google_results, 1):
                print(f"   {i}. {google_result['title']}")
                print(f"      {google_result['url']}")
            print()
            
            # 显示Reddit帖子
            print(f"📝 Reddit帖子 ({len(result.reddit_posts)} 个):")
            for i, post_data in enumerate(result.reddit_posts, 1):
                post = post_data['post']
                print(f"   {i}. {post['title']}")
                print(f"      子版块: r/{post['subreddit']}")
                print(f"      评分: {post['score']}, 评论数: {post['num_comments']}")
                print(f"      评论者: {len(post_data['commenters'])} 个")
            print()
            
            # 显示子版块分类结果
            if result.subreddit_classification:
                print("🏷️  子版块分类结果:")
                for category, subreddits in result.subreddit_classification.items():
                    print(f"   {category}: {len(subreddits)} 个子版块")
                    print(f"      {', '.join(subreddits[:5])}{'...' if len(subreddits) > 5 else ''}")
                print()
                
                # 显示分类摘要
                if result.classification_summary:
                    summary = result.classification_summary
                    print(f"📈 分类摘要:")
                    print(f"   总类别数: {summary['total_categories']}")
                    print(f"   总子版块数: {summary['total_subreddits']}")
                    print()
            
            # 显示用户历史数据统计
            if result.commenters_history:
                print(f"👥 用户历史数据 ({len(result.commenters_history)} 个用户):")
                
                for username, user_data in list(result.commenters_history.items())[:3]:  # 只显示前3个用户
                    print(f"   用户: {username}")
                    
                    if isinstance(user_data, dict):
                        for category, category_data in user_data.items():
                            if isinstance(category_data, dict) and 'stats' in category_data:
                                stats = category_data['stats']
                                print(f"     {category}: {stats['total_posts']} 帖子, {stats['total_comments']} 评论")
                    print()
                
                if len(result.commenters_history) > 3:
                    print(f"   ... 还有 {len(result.commenters_history) - 3} 个用户")
                print()
            
            print("🎉 演示完成!")
            
        else:
            print(f"❌ 搜索失败: {result.error_message}")
            
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        await cogbridges_service.close()


async def demo_classification_only():
    """仅演示分类功能"""
    print("\n=== 仅演示子版块分类功能 ===\n")
    
    from services.subreddit_classifier import subreddit_classifier
    
    # 模拟从多个用户提取的子版块
    sample_subreddits = [
        # AI/技术相关
        "MachineLearning", "artificial", "ChatGPT", "OpenAI", "singularity",
        "programming", "Python", "javascript", "webdev", "technology",
        
        # 娱乐
        "movies", "television", "netflix", "gaming", "pcgaming", "funny",
        
        # 新闻/讨论
        "news", "worldnews", "politics", "AskReddit", "explainlikeimfive",
        
        # 生活方式
        "cooking", "fitness", "travel", "photography", "books",
        
        # 投资/金融
        "investing", "stocks", "cryptocurrency", "wallstreetbets",
        
        # 科学
        "science", "space", "physics", "biology"
    ]
    
    print(f"📋 待分类的子版块 ({len(sample_subreddits)} 个):")
    print(f"   {', '.join(sample_subreddits)}")
    print()
    
    try:
        print("🤖 使用LLM进行智能分类...")
        classification = await subreddit_classifier.classify_subreddits(
            sample_subreddits,
            target_subreddit="ChatGPT"  # 以ChatGPT为参考
        )
        
        if classification:
            print("✅ 分类完成!\n")
            
            print("🏷️  分类结果:")
            for category, subreddits in classification.items():
                print(f"   📁 {category} ({len(subreddits)} 个):")
                print(f"      {', '.join(subreddits)}")
                print()
            
            # 获取详细摘要
            summary = subreddit_classifier.get_category_summary(classification)
            print("📊 详细统计:")
            for category, details in summary['categories'].items():
                print(f"   {category}: {details['count']} 个 ({details['percentage']}%)")
            
        else:
            print("❌ 分类失败")
            
    except Exception as e:
        print(f"❌ 分类过程中出错: {e}")


async def main():
    """主函数"""
    print("🎯 CogBridges 新功能演示\n")
    
    # 选择演示模式
    print("请选择演示模式:")
    print("1. 完整搜索演示（包含Google搜索、Reddit获取、子版块分类）")
    print("2. 仅子版块分类演示")
    print("3. 两个都演示")
    
    choice = input("\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        await demo_new_search_feature()
    elif choice == "2":
        await demo_classification_only()
    elif choice == "3":
        await demo_classification_only()
        await demo_new_search_feature()
    else:
        print("无效选择，默认执行分类演示")
        await demo_classification_only()


if __name__ == "__main__":
    asyncio.run(main())
