"""
CogBridges Search - 代理配置工具
提供代理设置和会话管理功能
"""

import os
import requests
import aiohttp
from typing import Optional, Dict, Any
from config import config
from utils.logger_utils import get_logger
import socks
import socket
from urllib.parse import urlparse
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from aiohttp_proxy import ProxyConnector

logger = get_logger(__name__)

# 全局会话，避免重复创建
_session = None
_proxy_session = None
_aio_session = None
_aio_proxy_connector = None

def get_session() -> requests.Session:
    """获取一个带重试机制的requests.Session"""
    global _session
    if _session is None:
        _session = requests.Session()
        # 设置重试策略
        retries = Retry(total=3, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504])
        _session.mount('http://', HTTPAdapter(max_retries=retries))
        _session.mount('https://', HTTPAdapter(max_retries=retries))
    return _session

def get_proxy_session() -> requests.Session:
    """获取一个配置了代理的requests.Session"""
    global _proxy_session
    if _proxy_session is None:
        if config.ENABLE_PROXY and (config.HTTP_PROXY or config.HTTPS_PROXY):
            logger.info(f"代理功能已启用，使用代理: HTTP={config.HTTP_PROXY}, HTTPS={config.HTTPS_PROXY}")
            proxies = {}
            if config.HTTP_PROXY:
                proxies["http"] = config.HTTP_PROXY
            if config.HTTPS_PROXY:
                proxies["https"] = config.HTTPS_PROXY
            
            _proxy_session = requests.Session()
            _proxy_session.proxies = proxies
            # 设置重试策略
            retries = Retry(total=3, backoff_factor=1, status_forcelist=[429, 500, 502, 503, 504])
            adapter = HTTPAdapter(max_retries=retries)
            _proxy_session.mount('http://', adapter)
            _proxy_session.mount('https://', adapter)
        else:
            logger.info("代理功能已禁用")
            _proxy_session = get_session() # 如果代理禁用，返回普通session
    return _proxy_session

async def get_aio_session() -> aiohttp.ClientSession:
    """获取一个异步的aiohttp.ClientSession"""
    global _aio_session
    if _aio_session is None or _aio_session.closed:
        if config.ENABLE_PROXY and (config.HTTP_PROXY or config.HTTPS_PROXY):
            logger.info(f"异步代理已启用: HTTP={config.HTTP_PROXY}, HTTPS={config.HTTPS_PROXY}")
            global _aio_proxy_connector
            if _aio_proxy_connector is None:
                # 优先使用HTTPS代理，如果没有则使用HTTP代理
                proxy_url = config.HTTPS_PROXY or config.HTTP_PROXY
                _aio_proxy_connector = ProxyConnector.from_url(proxy_url)
            _aio_session = aiohttp.ClientSession(connector=_aio_proxy_connector)
        else:
            logger.info("异步代理已禁用")
            _aio_session = aiohttp.ClientSession()
    return _aio_session

def close_sessions():
    """关闭所有会话"""
    global _session, _proxy_session
    if _session:
        _session.close()
        _session = None
    if _proxy_session:
        _proxy_session.close()
        _proxy_session = None

async def close_aio_sessions():
    """关闭所有异步会话"""
    global _aio_session, _aio_proxy_connector
    if _aio_session and not _aio_session.closed:
        await _aio_session.close()
        _aio_session = None
    if _aio_proxy_connector:
        # aiohttp-proxy's connector does not have a close method
        _aio_proxy_connector = None


def setup_proxy() -> Optional[Dict[str, str]]:
    """
    设置代理配置
    
    Returns:
        代理配置字典，如果未配置则返回None
    """
    # 检查代理开关
    if not config.ENABLE_PROXY:
        logger.info("代理功能已禁用")
        return None
    
    proxy_dict = config.proxy_dict
    
    if proxy_dict:
        # 设置环境变量，确保所有HTTP库都能使用代理
        if "http" in proxy_dict:
            os.environ["HTTP_PROXY"] = proxy_dict["http"]
        if "https" in proxy_dict:
            os.environ["HTTPS_PROXY"] = proxy_dict["https"]
        
        logger.info(f"代理配置已设置: {proxy_dict}")
        return proxy_dict
    else:
        logger.info("代理已启用但未配置代理地址")
        return None


def get_proxy_session() -> requests.Session:
    """
    获取配置了代理的requests会话
    
    Returns:
        配置了代理的requests.Session对象
    """
    session = requests.Session()
    
    proxy_dict = setup_proxy()
    if proxy_dict:
        session.proxies.update(proxy_dict)
        logger.debug("requests会话已配置代理")
    
    # 设置通用请求头
    session.headers.update({
        'User-Agent': 'CogBridges-Search/1.0 (Reddit Analysis Tool)',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    })
    
    # 设置更长的超时时间，特别是对于代理连接
    if proxy_dict:
        session.timeout = (30, 60)  # (连接超时, 读取超时)
    else:
        session.timeout = config.REQUEST_TIMEOUT
    
    return session


async def get_async_proxy_session() -> aiohttp.ClientSession:
    """
    获取配置了代理的异步HTTP会话
    
    Returns:
        配置了代理的aiohttp.ClientSession对象
    """
    proxy_dict = setup_proxy()
    
    # 创建连接器
    connector_kwargs = {
        'limit': config.MAX_CONCURRENT_REQUESTS,
        'limit_per_host': 5,
        'ttl_dns_cache': 300,
        'use_dns_cache': True,
    }
    
    # 如果配置了代理，设置代理连接器
    if proxy_dict and proxy_dict.get("http"):
        connector_kwargs['trust_env'] = True
        logger.debug("aiohttp会话已配置代理")
    
    connector = aiohttp.TCPConnector(**connector_kwargs)
    
    # 设置超时
    timeout = aiohttp.ClientTimeout(
        total=config.REQUEST_TIMEOUT,
        connect=10,
        sock_read=config.REQUEST_TIMEOUT
    )
    
    # 设置请求头
    headers = {
        'User-Agent': 'CogBridges-Search/1.0 (Reddit Analysis Tool)',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
    }
    
    session = aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers=headers,
        trust_env=True  # 使用环境变量中的代理设置
    )
    
    return session


def test_proxy_connection(test_url: str = "https://httpbin.org/ip") -> bool:
    """
    测试代理连接是否正常
    
    Args:
        test_url: 测试URL
        
    Returns:
        连接是否成功
    """
    try:
        session = get_proxy_session()
        response = session.get(test_url, timeout=10)
        
        if response.status_code == 200:
            logger.info("代理连接测试成功")
            
            # 如果是httpbin，显示IP信息
            if "httpbin.org" in test_url:
                try:
                    ip_info = response.json()
                    logger.info(f"当前IP: {ip_info.get('origin', 'Unknown')}")
                except:
                    pass
            
            return True
        else:
            logger.warning(f"代理连接测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"代理连接测试异常: {e}")
        return False


async def test_async_proxy_connection(test_url: str = "https://httpbin.org/ip") -> bool:
    """
    测试异步代理连接是否正常
    
    Args:
        test_url: 测试URL
        
    Returns:
        连接是否成功
    """
    try:
        async with await get_async_proxy_session() as session:
            async with session.get(test_url) as response:
                if response.status == 200:
                    logger.info("异步代理连接测试成功")
                    
                    # 如果是httpbin，显示IP信息
                    if "httpbin.org" in test_url:
                        try:
                            ip_info = await response.json()
                            logger.info(f"当前IP: {ip_info.get('origin', 'Unknown')}")
                        except:
                            pass
                    
                    return True
                else:
                    logger.warning(f"异步代理连接测试失败: HTTP {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"异步代理连接测试异常: {e}")
        return False


def get_proxy_info() -> Dict[str, Any]:
    """
    获取代理配置信息
    
    Returns:
        代理配置信息字典
    """
    proxy_dict = config.proxy_dict
    
    info = {
        "proxy_configured": config.proxy_configured,
        "proxy_settings": proxy_dict,
        "environment_variables": {
            "HTTP_PROXY": os.environ.get("HTTP_PROXY", ""),
            "HTTPS_PROXY": os.environ.get("HTTPS_PROXY", ""),
        }
    }
    
    return info


if __name__ == "__main__":
    # 测试代理配置
    print("CogBridges Search - 代理配置测试")
    print("=" * 50)
    
    # 显示代理信息
    proxy_info = get_proxy_info()
    print("📡 代理配置信息:")
    for key, value in proxy_info.items():
        print(f"  {key}: {value}")
    
    # 测试代理连接
    print("\n🔗 测试代理连接...")
    success = test_proxy_connection()
    
    if success:
        print("✅ 代理连接正常")
    else:
        print("❌ 代理连接失败")
        print("请检查代理设置和网络连接")
