{"timestamp": "2025-07-19T20:03:54.961949", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1h8ox94", "subreddit": "singularity", "title": "How does Gemini, Grok or Llama compare to GPT or C", "score": 17, "num_comments": 13}, "timestamp": "2025-07-19T20:03:54.961948"}}
{"timestamp": "2025-07-19T20:03:55.080413", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1j42134", "subreddit": "grok", "title": "Would you say Grok 3 is better than GPT 4.5 or Cla", "score": 34, "num_comments": 77}, "timestamp": "2025-07-19T20:03:55.080413"}}
{"timestamp": "2025-07-19T20:03:55.502079", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dqj1lg", "subreddit": "ClaudeAI", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspec", "score": 228, "num_comments": 139}, "timestamp": "2025-07-19T20:03:55.502079"}}
{"timestamp": "2025-07-19T20:03:55.775590", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1jnl8pn", "subreddit": "ClaudeAI", "title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ", "score": 555, "num_comments": 162}, "timestamp": "2025-07-19T20:03:55.775590"}}
{"timestamp": "2025-07-19T20:03:56.053070", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1lwdzjd", "subreddit": "singularity", "title": "SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude", "score": 331, "num_comments": 90}, "timestamp": "2025-07-19T20:03:56.053070"}}
{"timestamp": "2025-07-19T20:03:56.360102", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1h8ox94", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T20:03:56.360102"}}
{"timestamp": "2025-07-19T20:03:57.692397", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1j42134", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T20:03:57.692396"}}
{"timestamp": "2025-07-19T20:03:58.478791", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1lwdzjd", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T20:03:58.478791"}}
{"timestamp": "2025-07-19T20:03:59.302188", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1jnl8pn", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T20:03:59.302188"}}
{"timestamp": "2025-07-19T20:03:59.545617", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dqj1lg", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T20:03:59.545617"}}
