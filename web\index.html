<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CogBridges - 智能搜索引擎</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- Logo区域 -->
            <div class="logo-section">
                <h1 class="logo">CogBridges</h1>
                <p class="tagline">连接认知，桥接智慧</p>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
                <form class="search-form" id="searchForm">
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                            <input 
                                type="text" 
                                class="search-input" 
                                id="searchInput"
                                placeholder="搜索Reddit上的讨论和见解..."
                                autocomplete="off"
                                spellcheck="false"
                            >
                            <button type="button" class="clear-button" id="clearButton" style="display: none;">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </button>
                        </div>
                        
                        <!-- 搜索建议下拉框 -->
                        <div class="suggestions-dropdown" id="suggestionsDropdown" style="display: none;">
                            <div class="suggestions-list" id="suggestionsList"></div>
                        </div>
                    </div>

                    <!-- 搜索按钮 -->
                    <div class="search-buttons">
                        <button type="submit" class="search-btn primary" id="searchButton">
                            <span class="btn-text">CogBridges 搜索</span>
                            <span class="btn-loading" style="display: none;">
                                <svg class="loading-spinner" viewBox="0 0 24 24">
                                    <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                        <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                        <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                                    </circle>
                                </svg>
                                搜索中...
                            </span>
                        </button>
                        <button type="button" class="search-btn secondary" id="luckyButton">手气不错</button>
                    </div>
                </form>

                <!-- 搜索选项 -->
                <div class="search-options">
                    <div class="option-group">
                        <label class="option-label">
                            <input type="checkbox" id="redditOnly" checked>
                            <span class="checkmark"></span>
                            仅搜索 Reddit
                        </label>
                        <label class="option-label">
                            <input type="checkbox" id="includeComments" checked>
                            <span class="checkmark"></span>
                            包含评论分析
                        </label>
                        <label class="option-label">
                            <input type="checkbox" id="userHistory" checked>
                            <span class="checkmark"></span>
                            用户历史分析
                        </label>
                        <label class="option-label">
                            <input type="checkbox" id="subredditClassification" checked>
                            <span class="checkmark"></span>
                            子版块智能分类
                        </label>
                    </div>
                    
                    <div class="advanced-options" id="advancedOptions" style="display: none;">
                        <div class="option-row">
                            <label for="maxResults">最大结果数:</label>
                            <select id="maxResults">
                                <option value="3">3</option>
                                <option value="5" selected>5</option>
                                <option value="10">10</option>
                            </select>
                        </div>
                        <div class="option-row">
                            <label for="maxComments">每帖评论数:</label>
                            <select id="maxComments">
                                <option value="3">3</option>
                                <option value="6" selected>6</option>
                                <option value="10">10</option>
                            </select>
                        </div>
                    </div>
                    
                    <button type="button" class="advanced-toggle" id="advancedToggle">
                        高级选项
                        <svg class="toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 快速链接 -->
            <div class="quick-links">
                <h3>热门话题</h3>
                <div class="links-grid">
                    <a href="#" class="quick-link" data-query="python programming tips">Python 编程</a>
                    <a href="#" class="quick-link" data-query="machine learning tutorial">机器学习</a>
                    <a href="#" class="quick-link" data-query="web development">Web 开发</a>
                    <a href="#" class="quick-link" data-query="data science">数据科学</a>
                    <a href="#" class="quick-link" data-query="artificial intelligence">人工智能</a>
                    <a href="#" class="quick-link" data-query="career advice">职业建议</a>
                </div>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="#about">关于 CogBridges</a>
                    <a href="#privacy">隐私政策</a>
                    <a href="#terms">使用条款</a>
                    <a href="#help">帮助</a>
                </div>
                <div class="footer-info">
                    <p>&copy; 2025 CogBridges. 连接认知，桥接智慧。</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- 结果页面模态框 -->
    <div class="modal" id="resultsModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>搜索结果</h2>
                <button class="modal-close" id="modalClose">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body" id="resultsContent">
                <!-- 搜索结果将在这里显示 -->
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
</body>
</html>
