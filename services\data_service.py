"""
CogBridges Search - 数据存储服务
实现JSON格式的数据存储和日志记录系统
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime
import hashlib
import gzip
import shutil

from config import config
from models.search_models import SearchResult
from models.reddit_models import RedditPost, RedditComment, UserHistory
from utils.logger_utils import get_logger


class DataService:
    """数据存储服务类"""
    
    def __init__(self):
        """初始化数据存储服务"""
        self.logger = get_logger(__name__)
        
        # 确保数据目录存在
        self.data_dir = config.DATA_DIR
        self.results_dir = config.RESULTS_DIR
        self.logs_dir = config.LOGS_DIR
        
        for directory in [self.data_dir, self.results_dir, self.logs_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("数据存储服务初始化成功")
    
    def generate_session_id(self, query: str) -> str:
        """
        生成会话ID
        
        Args:
            query: 搜索查询
            
        Returns:
            会话ID
        """
        # 使用查询和时间戳生成唯一ID
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        query_hash = hashlib.md5(query.encode('utf-8')).hexdigest()[:8]
        return f"{timestamp}_{query_hash}"
    
    def save_search_result(self, search_result: SearchResult, session_id: str = None) -> str:
        """
        保存搜索结果
        
        Args:
            search_result: 搜索结果对象
            session_id: 会话ID（可选）
            
        Returns:
            保存的文件路径
        """
        if not session_id:
            session_id = self.generate_session_id(search_result.query.query)
        
        # 创建文件名
        filename = f"search_result_{session_id}.json"
        filepath = self.results_dir / filename
        
        try:
            # 保存为JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(search_result.to_dict(), f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"搜索结果已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"保存搜索结果失败: {e}")
            raise
    
    def save_reddit_data(self, reddit_data: Dict[str, Any], session_id: str) -> str:
        """
        保存Reddit数据
        
        Args:
            reddit_data: Reddit数据字典
            session_id: 会话ID
            
        Returns:
            保存的文件路径
        """
        filename = f"reddit_data_{session_id}.json"
        filepath = self.results_dir / filename
        
        try:
            # 转换数据为可序列化格式
            serializable_data = self._make_serializable(reddit_data)
            
            # 保存为JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Reddit数据已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"保存Reddit数据失败: {e}")
            raise
    
    def save_complete_session(
        self,
        session_id: str,
        search_result: SearchResult,
        reddit_data: Dict[str, Any],
        metadata: Dict[str, Any] = None
    ) -> str:
        """
        保存完整的会话数据
        
        Args:
            session_id: 会话ID
            search_result: 搜索结果
            reddit_data: Reddit数据
            metadata: 元数据（可选）
            
        Returns:
            保存的文件路径
        """
        filename = f"complete_session_{session_id}.json"
        filepath = self.results_dir / filename
        
        try:
            # 构建完整会话数据
            complete_data = {
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "search_result": search_result.to_dict(),
                "reddit_data": self._make_serializable(reddit_data),
                "metadata": metadata or {},
                "statistics": {
                    "search_results_count": len(search_result.results),
                    "reddit_posts_count": len(reddit_data.get("posts_data", [])),
                    "total_commenters": reddit_data.get("statistics", {}).get("total_commenters", 0),
                    "total_user_histories": reddit_data.get("statistics", {}).get("total_user_histories", 0),
                    "processing_time": reddit_data.get("statistics", {}).get("processing_time", 0)
                }
            }
            
            # 保存为JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(complete_data, f, ensure_ascii=False, indent=2)
            
            # 如果启用了压缩，创建压缩版本
            if config.ENABLE_CACHE:
                self._create_compressed_backup(filepath)
            
            self.logger.info(f"完整会话数据已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"保存完整会话数据失败: {e}")
            raise
    
    def load_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        加载会话数据
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话数据字典
        """
        filename = f"complete_session_{session_id}.json"
        filepath = self.results_dir / filename
        
        try:
            if not filepath.exists():
                self.logger.warning(f"会话文件不存在: {filepath}")
                return None
            
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.info(f"会话数据加载成功: {session_id}")
            return data
            
        except Exception as e:
            self.logger.error(f"加载会话数据失败: {e}")
            return None
    
    def list_sessions(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        列出最近的会话
        
        Args:
            limit: 返回数量限制
            
        Returns:
            会话信息列表
        """
        try:
            sessions = []
            
            # 查找所有会话文件
            pattern = "complete_session_*.json"
            session_files = list(self.results_dir.glob(pattern))
            
            # 按修改时间排序
            session_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            for filepath in session_files[:limit]:
                try:
                    # 从文件名提取会话ID
                    session_id = filepath.stem.replace("complete_session_", "")
                    
                    # 获取文件信息
                    stat = filepath.stat()
                    
                    # 尝试读取基本信息
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    session_info = {
                        "session_id": session_id,
                        "timestamp": data.get("timestamp", ""),
                        "query": data.get("search_result", {}).get("query", {}).get("query", ""),
                        "file_size": stat.st_size,
                        "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "statistics": data.get("statistics", {})
                    }
                    
                    sessions.append(session_info)
                    
                except Exception as e:
                    self.logger.warning(f"读取会话文件失败 {filepath}: {e}")
                    continue
            
            return sessions
            
        except Exception as e:
            self.logger.error(f"列出会话失败: {e}")
            return []
    
    def delete_session(self, session_id: str) -> bool:
        """
        删除会话数据
        
        Args:
            session_id: 会话ID
            
        Returns:
            删除是否成功
        """
        try:
            # 删除相关文件
            patterns = [
                f"complete_session_{session_id}.json",
                f"complete_session_{session_id}.json.gz",
                f"search_result_{session_id}.json",
                f"reddit_data_{session_id}.json"
            ]
            
            deleted_count = 0
            for pattern in patterns:
                filepath = self.results_dir / pattern
                if filepath.exists():
                    filepath.unlink()
                    deleted_count += 1
            
            if deleted_count > 0:
                self.logger.info(f"会话数据已删除: {session_id} ({deleted_count} 个文件)")
                return True
            else:
                self.logger.warning(f"未找到会话数据: {session_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除会话数据失败: {e}")
            return False
    
    def _make_serializable(self, data: Any) -> Any:
        """
        将数据转换为可序列化格式
        
        Args:
            data: 原始数据
            
        Returns:
            可序列化的数据
        """
        if isinstance(data, dict):
            return {key: self._make_serializable(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._make_serializable(item) for item in data]
        elif hasattr(data, 'to_dict'):
            return data.to_dict()
        elif isinstance(data, (datetime,)):
            return data.isoformat()
        else:
            return data
    
    def _create_compressed_backup(self, filepath: Path):
        """
        创建压缩备份
        
        Args:
            filepath: 原文件路径
        """
        try:
            compressed_path = filepath.with_suffix(filepath.suffix + '.gz')
            
            with open(filepath, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            self.logger.debug(f"压缩备份已创建: {compressed_path}")
            
        except Exception as e:
            self.logger.warning(f"创建压缩备份失败: {e}")
    
    def get_storage_statistics(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            存储统计信息字典
        """
        try:
            # 统计文件数量和大小
            total_files = 0
            total_size = 0
            
            for filepath in self.results_dir.rglob("*"):
                if filepath.is_file():
                    total_files += 1
                    total_size += filepath.stat().st_size
            
            # 统计会话数量
            session_files = list(self.results_dir.glob("complete_session_*.json"))
            
            return {
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "session_count": len(session_files),
                "data_directory": str(self.data_dir),
                "results_directory": str(self.results_dir),
                "logs_directory": str(self.logs_dir)
            }
            
        except Exception as e:
            self.logger.error(f"获取存储统计失败: {e}")
            return {}


if __name__ == "__main__":
    # 测试数据存储服务
    print("CogBridges Search - 数据存储服务测试")
    print("=" * 50)
    
    try:
        # 创建数据服务
        data_service = DataService()
        
        # 测试生成会话ID
        print("🆔 测试会话ID生成...")
        test_query = "python programming"
        session_id = data_service.generate_session_id(test_query)
        print(f"生成的会话ID: {session_id}")
        
        # 测试保存测试数据
        print("\n💾 测试数据保存...")
        test_data = {
            "test": True,
            "timestamp": datetime.now().isoformat(),
            "data": {"key": "value"}
        }
        
        # 模拟保存会话数据
        from ..models.search_models import SearchQuery, SearchResult
        
        search_query = SearchQuery(query=test_query)
        search_result = SearchResult(query=search_query, success=True)
        
        filepath = data_service.save_complete_session(
            session_id, search_result, test_data
        )
        print(f"✅ 数据保存成功: {filepath}")
        
        # 测试加载数据
        print("\n📂 测试数据加载...")
        loaded_data = data_service.load_session_data(session_id)
        if loaded_data:
            print("✅ 数据加载成功")
            print(f"   查询: {loaded_data.get('search_result', {}).get('query', {}).get('query', 'N/A')}")
        else:
            print("❌ 数据加载失败")
        
        # 测试列出会话
        print("\n📋 测试会话列表...")
        sessions = data_service.list_sessions(limit=5)
        print(f"找到 {len(sessions)} 个会话")
        
        # 显示存储统计
        print("\n📊 存储统计:")
        stats = data_service.get_storage_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 清理测试数据
        print(f"\n🗑️ 清理测试数据...")
        if data_service.delete_session(session_id):
            print("✅ 测试数据清理成功")
        else:
            print("⚠️ 测试数据清理失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
