#!/usr/bin/env python3
"""
CogBridges - 统一启动脚本
同时启动后端API服务器和前端Web界面的单一启动脚本
"""

import sys
import os
import subprocess
import threading
import time
import webbrowser
from pathlib import Path
import signal
import atexit

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger_utils import get_logger


class CogBridgesLauncher:
    """CogBridges统一启动器"""
    
    def __init__(self):
        """初始化启动器"""
        self.logger = get_logger(__name__)
        self.backend_process = None
        self.frontend_server = None
        self.running = False
        
        # 注册退出处理
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查依赖...")
        
        # 定义包名和对应的导入名
        package_mappings = {
            'flask': 'flask',
            'flask_cors': 'flask_cors', 
            'requests': 'requests',
            'praw': 'praw',
            'asyncpraw': 'asyncpraw',
            'tenacity': 'tenacity',
            'python-dotenv': 'dotenv'
        }
        
        missing_packages = []
        
        for package, import_name in package_mappings.items():
            try:
                __import__(import_name)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少以下依赖包:")
            for package in missing_packages:
                print(f"   - {package}")
            print(f"\n💡 请运行以下命令安装依赖:")
            print(f"   pip install -r requirements.txt")
            return False
        
        print("✅ 所有依赖已安装")
        return True
    
    def check_configuration(self):
        """检查配置"""
        print("🔧 检查配置...")
        
        # 检查必要的配置项
        required_configs = [
            ('GOOGLE_API_KEY', config.GOOGLE_API_KEY),
            ('GOOGLE_SEARCH_ENGINE_ID', config.GOOGLE_SEARCH_ENGINE_ID),
            ('REDDIT_CLIENT_ID', config.REDDIT_CLIENT_ID),
            ('REDDIT_CLIENT_SECRET', config.REDDIT_CLIENT_SECRET)
        ]
        
        missing_configs = []
        
        for name, value in required_configs:
            if not value or value == "your_api_key_here":
                missing_configs.append(name)
        
        if missing_configs:
            print(f"❌ 缺少以下配置:")
            for config_name in missing_configs:
                print(f"   - {config_name}")
            print(f"\n💡 请在config.py中配置这些参数")
            return False
        
        print("✅ 配置检查通过")
        return True
    
    def start_backend_api(self):
        """启动后端API服务器"""
        print("🚀 启动后端API服务器...")
        
        try:
            # 创建Flask应用
            from flask import Flask, jsonify, request
            from flask_cors import CORS
            from services.cogbridges_service import CogBridgesService
            
            app = Flask(__name__)
            CORS(app)
            
            # 添加一个简单的测试路由
            @app.route('/test', methods=['GET'])
            def test():
                """简单测试接口"""
                return jsonify({
                    "message": "Flask app is running",
                    "timestamp": time.time(),
                    "cogbridges_service_status": "initialized" if cogbridges_service else "not_initialized"
                })
            
            @app.route('/api/health', methods=['GET'])
            def health_check():
                """健康检查接口"""
                try:
                    return jsonify({
                        "status": "healthy",
                        "service": "CogBridges API",
                        "timestamp": time.time()
                    })
                except Exception as e:
                    return jsonify({
                        "status": "error",
                        "error": str(e)
                    }), 500
            
            # 初始化CogBridges服务
            cogbridges_service = None
            try:
                cogbridges_service = CogBridgesService()
                print("✅ CogBridges服务初始化成功")
            except Exception as e:
                self.logger.error(f"CogBridges服务初始化失败: {e}")
                print(f"⚠️ CogBridges服务初始化失败: {e}")
                print("📝 继续启动，但某些功能可能不可用")
            
            @app.route('/api/search', methods=['POST'])
            def search():
                """统一搜索接口 - 默认使用增强功能"""
                try:
                    # 检查服务是否可用
                    if not cogbridges_service:
                        return jsonify({
                            "success": False,
                            "error": "CogBridges服务未初始化"
                        }), 503
                    
                    data = request.get_json()
                    query = data.get('query', '')
                    use_classification = data.get('use_classification', True)  # 默认启用分类

                    if not query:
                        return jsonify({
                            "success": False,
                            "error": "查询参数不能为空"
                        }), 400

                    # 执行搜索（同步调用异步函数）
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        # 根据参数选择搜索方法
                        if use_classification:
                            result = loop.run_until_complete(
                                cogbridges_service.search_with_subreddit_classification(query)
                            )
                        else:
                            result = loop.run_until_complete(
                                cogbridges_service.search(query)
                            )

                        # 转换结果为JSON格式
                        response_data = {
                            "success": result.success,
                            "query": result.query,
                            "session_id": result.session_id,
                            "timestamp": result.timestamp.isoformat(),
                            "total_time": result.total_time,
                            "google_results": result.google_results,
                            "reddit_posts": result.reddit_posts,
                            "commenters_history": result.commenters_history,
                            "statistics": {
                                "google_search_time": result.google_search_time,
                                "reddit_posts_time": result.reddit_posts_time,
                                "commenters_history_time": result.commenters_history_time,
                                "google_results_count": len(result.google_results) if result.google_results else 0,
                                "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                                "commenters_count": len(result.commenters_history) if result.commenters_history else 0
                            }
                        }

                        # 如果使用了分类功能，添加分类相关字段
                        if use_classification and hasattr(result, 'subreddit_classification'):
                            response_data["subreddit_classification"] = result.subreddit_classification
                            response_data["classification_summary"] = result.classification_summary
                            response_data["statistics"]["classification_categories"] = len(result.subreddit_classification) if result.subreddit_classification else 0

                        if not result.success:
                            response_data["error"] = result.error_message

                        return jsonify(response_data)

                    finally:
                        loop.close()

                except Exception as e:
                    self.logger.error(f"搜索请求处理失败: {e}")
                    return jsonify({
                        "success": False,
                        "error": f"服务器内部错误: {str(e)}"
                    }), 500

            @app.route('/api/status', methods=['GET'])
            def get_status():
                """获取服务状态接口"""
                try:
                    if not cogbridges_service:
                        return jsonify({
                            "service": "CogBridges API",
                            "timestamp": time.time(),
                            "status": "service_unavailable",
                            "error": "CogBridges服务未初始化"
                        }), 503
                    
                    stats = cogbridges_service.get_statistics()

                    # 检查各个服务的状态
                    status_info = {
                        "service": "CogBridges API",
                        "timestamp": time.time(),
                        "version": "2.0.0",
                        "features": {
                            "basic_search": True,
                            "subreddit_classification": True,
                            "enhanced_comments": True
                        },
                        "services": {
                            "google_search": bool(stats.get("google_stats", {}).get("configured", False)),
                            "reddit_api": bool(stats.get("reddit_stats", {}).get("configured", False)),
                            "llm_service": bool(stats.get("llm_stats", {}).get("configured", False))
                        },
                        "statistics": stats
                    }

                    return jsonify(status_info)

                except Exception as e:
                    self.logger.error(f"状态查询失败: {e}")
                    return jsonify({
                        "service": "CogBridges API",
                        "timestamp": time.time(),
                        "error": f"状态查询失败: {str(e)}"
                    }), 500
            
            # 启动Flask应用 - 使用直接启动而不是线程
            print(f"📍 正在启动Flask服务器: {config.HOST}:{config.PORT}")
            
            # 检查端口是否可用
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((config.HOST, config.PORT))
            sock.close()
            
            if result == 0:
                print(f"⚠️ 端口 {config.PORT} 已被占用，尝试使用其他端口")
                config.PORT = config.PORT + 10
                print(f"🔄 改用端口: {config.PORT}")
            
            # 在单独线程中启动Flask应用
            def run_flask():
                try:
                    print(f"🔧 Flask配置: host={config.HOST}, port={config.PORT}")
                    app.run(
                        host=config.HOST,
                        port=config.PORT,
                        debug=True,  # 启用调试模式
                        threaded=True,
                        use_reloader=False  # 关闭reloader避免线程冲突
                    )
                except Exception as e:
                    print(f"❌ Flask应用启动失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            backend_thread = threading.Thread(target=run_flask, daemon=True)
            backend_thread.start()
            
            # 等待服务器启动
            print("⏳ 等待Flask服务器启动...")
            time.sleep(8)  # 增加等待时间
            
            # 测试API是否正常
            import requests
            
            # 首先测试简单路由
            try:
                print(f"🔍 正在测试简单路由: http://{config.HOST}:{config.PORT}/test")
                test_response = requests.get(f"http://{config.HOST}:{config.PORT}/test", timeout=10)
                print(f"📡 简单路由响应状态码: {test_response.status_code}")
                if test_response.status_code == 200:
                    print(f"📄 简单路由响应内容: {test_response.text}")
                    print("✅ 简单路由测试成功")
                else:
                    print(f"❌ 简单路由响应异常: {test_response.status_code}")
                    print(f"📄 响应内容: {test_response.text[:200]}...")
            except requests.RequestException as e:
                print(f"❌ 简单路由连接失败: {e}")
                return False
            
            # 然后测试健康检查路由
            try:
                print(f"🔍 正在测试API连接: http://{config.HOST}:{config.PORT}/api/health")
                response = requests.get(f"http://{config.HOST}:{config.PORT}/api/health", timeout=10)
                print(f"📡 API响应状态码: {response.status_code}")
                if response.status_code == 200:
                    print(f"✅ 后端API服务器启动成功: http://{config.HOST}:{config.PORT}")
                    return True
                else:
                    print(f"❌ 后端API服务器响应异常: {response.status_code}")
                    print(f"📄 响应内容: {response.text[:200]}...")
                    return False
            except requests.RequestException as e:
                print(f"❌ 后端API服务器连接失败: {e}")
                print(f"💡 可能的原因: 端口被占用、防火墙阻止、或服务器启动失败")
                return False
                
        except Exception as e:
            self.logger.error(f"后端API服务器启动失败: {e}")
            print(f"❌ 后端API服务器启动失败: {e}")
            return False
    
    def start_frontend_server(self):
        """启动前端Web服务器"""
        print("🌐 启动前端Web服务器...")
        
        try:
            import http.server
            import socketserver
            from functools import partial
            
            # 设置前端文件目录
            web_dir = project_root / "web"
            if not web_dir.exists():
                print(f"❌ 前端文件目录不存在: {web_dir}")
                return False
            
            # 自定义HTTP请求处理器
            class CogBridgesHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
                def __init__(self, *args, **kwargs):
                    super().__init__(*args, directory=str(web_dir), **kwargs)
                
                def end_headers(self):
                    # 添加CORS头
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    super().end_headers()
            
            # 启动HTTP服务器
            frontend_port = config.PORT + 1  # 前端端口为API端口+1
            
            def run_frontend_server():
                with socketserver.TCPServer(("", frontend_port), CogBridgesHTTPRequestHandler) as httpd:
                    self.frontend_server = httpd
                    print(f"✅ 前端Web服务器启动成功: http://localhost:{frontend_port}")
                    httpd.serve_forever()
            
            frontend_thread = threading.Thread(target=run_frontend_server, daemon=True)
            frontend_thread.start()
            
            time.sleep(1)
            return True
            
        except Exception as e:
            self.logger.error(f"前端Web服务器启动失败: {e}")
            print(f"❌ 前端Web服务器启动失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        frontend_port = config.PORT + 1
        url = f"http://localhost:{frontend_port}"
        
        print(f"🌐 正在打开浏览器: {url}")
        
        try:
            webbrowser.open(url)
            print("✅ 浏览器已打开")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"💡 请手动访问: {url}")
    
    def display_startup_info(self):
        """显示启动信息"""
        print("\n" + "=" * 60)
        print("🎉 CogBridges 启动成功！")
        print("=" * 60)
        print(f"🔗 后端API地址: http://{config.HOST}:{config.PORT}")
        print(f"🌐 前端Web地址: http://localhost:{config.PORT + 1}")
        print(f"📚 API文档: http://{config.HOST}:{config.PORT}/api/health")
        print("-" * 60)
        print("💡 使用说明:")
        print("  1. 在Web界面中输入搜索查询")
        print("  2. 系统将自动执行Google搜索 → Reddit数据获取 → 用户分析")
        print("  3. 查看完整的分析结果和用户画像")
        print("-" * 60)
        print("🛑 按 Ctrl+C 停止服务")
        print("=" * 60)
    
    def run(self):
        """运行启动器"""
        print("🌟 CogBridges 统一启动器")
        print("=" * 60)
        
        # 检查依赖和配置
        if not self.check_dependencies():
            return False
        
        if not self.check_configuration():
            return False
        
        # 创建Flask应用
        flask_app = self.start_backend_api_direct()
        if not flask_app:
            return False
        
        # 启动前端Web服务器
        if not self.start_frontend_server():
            return False
        
        # 显示启动信息
        self.display_startup_info()
        
        # 打开浏览器
        self.open_browser()
        
        # 启动Flask应用
        print(f"🚀 正在启动Flask服务器: {config.HOST}:{config.PORT}")
        print("⏳ Flask应用即将启动，请稍候...")
        
        try:
            flask_app.run(
                host=config.HOST,
                port=config.PORT,
                debug=True,
                threaded=True
            )
        except Exception as e:
            print(f"❌ Flask应用运行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
    
    def start_backend_api_direct(self):
        """直接启动后端API服务器（非线程模式）"""
        print("🚀 启动后端API服务器（直接模式）...")
        
        try:
            # 创建Flask应用
            from flask import Flask, jsonify, request
            from flask_cors import CORS
            from services.cogbridges_service import CogBridgesService
            
            app = Flask(__name__)
            CORS(app)
            
            # 添加一个简单的测试路由
            @app.route('/test', methods=['GET'])
            def test():
                """简单测试接口"""
                return jsonify({
                    "message": "Flask app is running",
                    "timestamp": time.time(),
                    "mode": "direct"
                })
            
            @app.route('/api/health', methods=['GET'])
            def health_check():
                """健康检查接口"""
                try:
                    return jsonify({
                        "status": "healthy",
                        "service": "CogBridges API",
                        "timestamp": time.time()
                    })
                except Exception as e:
                    return jsonify({
                        "status": "error",
                        "error": str(e)
                    }), 500
            
            # 初始化CogBridges服务
            cogbridges_service = None
            try:
                cogbridges_service = CogBridgesService()
                print("✅ CogBridges服务初始化成功")
            except Exception as e:
                self.logger.error(f"CogBridges服务初始化失败: {e}")
                print(f"⚠️ CogBridges服务初始化失败: {e}")
                print("📝 继续启动，但某些功能可能不可用")
            
            @app.route('/api/search', methods=['POST'])
            def search():
                """统一搜索接口 - 默认使用增强功能"""
                try:
                    # 检查服务是否可用
                    if not cogbridges_service:
                        return jsonify({
                            "success": False,
                            "error": "CogBridges服务未初始化"
                        }), 503
                    
                    data = request.get_json()
                    query = data.get('query', '')
                    use_classification = data.get('use_classification', True)  # 默认启用分类

                    if not query:
                        return jsonify({
                            "success": False,
                            "error": "查询参数不能为空"
                        }), 400

                    # 执行搜索（同步调用异步函数）
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    try:
                        # 根据参数选择搜索方法
                        if use_classification:
                            result = loop.run_until_complete(
                                cogbridges_service.search_with_subreddit_classification(query)
                            )
                        else:
                            result = loop.run_until_complete(
                                cogbridges_service.search(query)
                            )

                        # 转换结果为JSON格式
                        response_data = {
                            "success": result.success,
                            "query": result.query,
                            "session_id": result.session_id,
                            "timestamp": result.timestamp.isoformat(),
                            "total_time": result.total_time,
                            "google_results": result.google_results,
                            "reddit_posts": result.reddit_posts,
                            "commenters_history": result.commenters_history,
                            "statistics": {
                                "google_search_time": result.google_search_time,
                                "reddit_posts_time": result.reddit_posts_time,
                                "commenters_history_time": result.commenters_history_time,
                                "google_results_count": len(result.google_results) if result.google_results else 0,
                                "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                                "commenters_count": len(result.commenters_history) if result.commenters_history else 0
                            }
                        }

                        # 如果使用了分类功能，添加分类相关字段
                        if use_classification and hasattr(result, 'subreddit_classification'):
                            response_data["subreddit_classification"] = result.subreddit_classification
                            response_data["classification_summary"] = result.classification_summary
                            response_data["statistics"]["classification_categories"] = len(result.subreddit_classification) if result.subreddit_classification else 0

                        if not result.success:
                            response_data["error"] = result.error_message

                        return jsonify(response_data)

                    finally:
                        loop.close()

                except Exception as e:
                    self.logger.error(f"搜索请求处理失败: {e}")
                    return jsonify({
                        "success": False,
                        "error": f"服务器内部错误: {str(e)}"
                    }), 500

            @app.route('/api/status', methods=['GET'])
            def get_status():
                """获取服务状态接口"""
                try:
                    if not cogbridges_service:
                        return jsonify({
                            "service": "CogBridges API",
                            "timestamp": time.time(),
                            "status": "service_unavailable",
                            "error": "CogBridges服务未初始化"
                        }), 503
                    
                    stats = cogbridges_service.get_statistics()

                    # 检查各个服务的状态
                    status_info = {
                        "service": "CogBridges API",
                        "timestamp": time.time(),
                        "version": "2.0.0",
                        "features": {
                            "basic_search": True,
                            "subreddit_classification": True,
                            "enhanced_comments": True
                        },
                        "services": {
                            "google_search": bool(stats.get("google_stats", {}).get("configured", False)),
                            "reddit_api": bool(stats.get("reddit_stats", {}).get("configured", False)),
                            "llm_service": bool(stats.get("llm_stats", {}).get("configured", False))
                        },
                        "statistics": stats
                    }

                    return jsonify(status_info)

                except Exception as e:
                    self.logger.error(f"状态查询失败: {e}")
                    return jsonify({
                        "service": "CogBridges API",
                        "timestamp": time.time(),
                        "error": f"状态查询失败: {str(e)}"
                    }), 500
            
            # 返回Flask应用对象，供主线程启动
            return app
                
        except Exception as e:
            self.logger.error(f"后端API服务器启动失败: {e}")
            print(f"❌ 后端API服务器启动失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 正在清理资源...")
        self.running = False
        
        if self.frontend_server:
            try:
                self.frontend_server.shutdown()
                print("✅ 前端服务器已停止")
            except:
                pass
        
        print("👋 CogBridges 已停止")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 收到信号 {signum}，正在停止服务...")
        self.cleanup()
        sys.exit(0)


def main():
    """主函数"""
    launcher = CogBridgesLauncher()
    
    try:
        success = launcher.run()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止...")
        launcher.cleanup()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
