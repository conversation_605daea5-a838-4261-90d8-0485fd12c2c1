// CogBridges - 搜索页面交互脚本

class CogBridgesSearch {
    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.setupSearchSuggestions();
    }

    initializeElements() {
        // 搜索相关元素
        this.searchForm = document.getElementById('searchForm');
        this.searchInput = document.getElementById('searchInput');
        this.clearButton = document.getElementById('clearButton');
        this.searchButton = document.getElementById('searchButton');
        this.luckyButton = document.getElementById('luckyButton');
        
        // 搜索建议
        this.suggestionsDropdown = document.getElementById('suggestionsDropdown');
        this.suggestionsList = document.getElementById('suggestionsList');
        
        // 搜索选项
        this.redditOnly = document.getElementById('redditOnly');
        this.includeComments = document.getElementById('includeComments');
        this.userHistory = document.getElementById('userHistory');
        this.maxResults = document.getElementById('maxResults');
        this.maxComments = document.getElementById('maxComments');
        
        // 高级选项
        this.advancedToggle = document.getElementById('advancedToggle');
        this.advancedOptions = document.getElementById('advancedOptions');
        
        // 模态框
        this.resultsModal = document.getElementById('resultsModal');
        this.modalClose = document.getElementById('modalClose');
        this.resultsContent = document.getElementById('resultsContent');
        
        // 快速链接
        this.quickLinks = document.querySelectorAll('.quick-link');
    }

    bindEvents() {
        // 搜索表单提交
        this.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
        
        // 搜索输入框事件
        this.searchInput.addEventListener('input', (e) => this.handleInputChange(e));
        this.searchInput.addEventListener('focus', () => this.showSuggestions());
        this.searchInput.addEventListener('blur', () => this.hideSuggestions());
        this.searchInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // 清除按钮
        this.clearButton.addEventListener('click', () => this.clearSearch());
        
        // 手气不错按钮
        this.luckyButton.addEventListener('click', () => this.handleLuckySearch());
        
        // 高级选项切换
        this.advancedToggle.addEventListener('click', () => this.toggleAdvancedOptions());
        
        // 模态框关闭
        this.modalClose.addEventListener('click', () => this.closeModal());
        this.resultsModal.addEventListener('click', (e) => {
            if (e.target === this.resultsModal) this.closeModal();
        });
        
        // 快速链接
        this.quickLinks.forEach(link => {
            link.addEventListener('click', (e) => this.handleQuickLink(e));
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') this.closeModal();
        });
    }

    setupSearchSuggestions() {
        // 预定义的搜索建议
        this.suggestions = [
            'python programming tips',
            'machine learning tutorial',
            'web development best practices',
            'data science career advice',
            'artificial intelligence news',
            'javascript frameworks comparison',
            'react vs vue',
            'docker tutorial',
            'kubernetes basics',
            'aws certification guide',
            'remote work tips',
            'startup advice',
            'investment strategies',
            'cryptocurrency explained',
            'fitness motivation'
        ];
        
        this.currentSuggestionIndex = -1;
    }

    handleInputChange(e) {
        const value = e.target.value.trim();
        
        // 显示/隐藏清除按钮
        this.clearButton.style.display = value ? 'block' : 'none';
        
        // 更新搜索建议
        this.updateSuggestions(value);
    }

    updateSuggestions(query) {
        if (!query) {
            this.hideSuggestions();
            return;
        }

        // 过滤建议
        const filteredSuggestions = this.suggestions.filter(suggestion =>
            suggestion.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5);

        if (filteredSuggestions.length === 0) {
            this.hideSuggestions();
            return;
        }

        // 渲染建议
        this.suggestionsList.innerHTML = filteredSuggestions.map((suggestion, index) => `
            <div class="suggestion-item" data-index="${index}" data-suggestion="${suggestion}">
                <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                </svg>
                <span>${this.highlightMatch(suggestion, query)}</span>
            </div>
        `).join('');

        // 绑定建议点击事件
        this.suggestionsList.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('mousedown', (e) => {
                e.preventDefault();
                this.selectSuggestion(item.dataset.suggestion);
            });
        });

        this.showSuggestions();
    }

    highlightMatch(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<strong>$1</strong>');
    }

    showSuggestions() {
        this.suggestionsDropdown.style.display = 'block';
    }

    hideSuggestions() {
        setTimeout(() => {
            this.suggestionsDropdown.style.display = 'none';
            this.currentSuggestionIndex = -1;
        }, 150);
    }

    selectSuggestion(suggestion) {
        this.searchInput.value = suggestion;
        this.hideSuggestions();
        this.clearButton.style.display = 'block';
        this.searchInput.focus();
    }

    handleKeyDown(e) {
        const suggestions = this.suggestionsList.querySelectorAll('.suggestion-item');
        
        if (suggestions.length === 0) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.currentSuggestionIndex = Math.min(
                    this.currentSuggestionIndex + 1,
                    suggestions.length - 1
                );
                this.updateSuggestionHighlight(suggestions);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.currentSuggestionIndex = Math.max(
                    this.currentSuggestionIndex - 1,
                    -1
                );
                this.updateSuggestionHighlight(suggestions);
                break;
                
            case 'Enter':
                if (this.currentSuggestionIndex >= 0) {
                    e.preventDefault();
                    const selectedSuggestion = suggestions[this.currentSuggestionIndex];
                    this.selectSuggestion(selectedSuggestion.dataset.suggestion);
                }
                break;
                
            case 'Escape':
                this.hideSuggestions();
                break;
        }
    }

    updateSuggestionHighlight(suggestions) {
        suggestions.forEach((item, index) => {
            item.classList.toggle('active', index === this.currentSuggestionIndex);
        });
    }

    clearSearch() {
        this.searchInput.value = '';
        this.clearButton.style.display = 'none';
        this.hideSuggestions();
        this.searchInput.focus();
    }

    toggleAdvancedOptions() {
        const isVisible = this.advancedOptions.style.display !== 'none';
        this.advancedOptions.style.display = isVisible ? 'none' : 'block';
        this.advancedToggle.classList.toggle('active', !isVisible);
    }

    handleQuickLink(e) {
        e.preventDefault();
        const query = e.target.dataset.query;
        this.searchInput.value = query;
        this.clearButton.style.display = 'block';
        this.handleSearch(e);
    }

    async handleSearch(e) {
        e.preventDefault();
        
        const query = this.searchInput.value.trim();
        if (!query) {
            this.showError('请输入搜索关键词');
            return;
        }

        this.setSearchLoading(true);
        
        try {
            // 构建搜索参数
            const searchParams = {
                query: query,
                reddit_only: this.redditOnly.checked,
                include_comments: this.includeComments.checked,
                user_history: this.userHistory.checked,
                max_results: parseInt(this.maxResults.value),
                max_comments: parseInt(this.maxComments.value)
            };

            // 模拟API调用（实际项目中这里会调用后端API）
            const results = await this.performSearch(searchParams);
            
            this.displayResults(results);
            
        } catch (error) {
            console.error('搜索失败:', error);
            this.showError('搜索失败，请稍后重试');
        } finally {
            this.setSearchLoading(false);
        }
    }

    async handleLuckySearch() {
        // 随机选择一个建议进行搜索
        const randomSuggestion = this.suggestions[Math.floor(Math.random() * this.suggestions.length)];
        this.searchInput.value = randomSuggestion;
        this.clearButton.style.display = 'block';
        
        const fakeEvent = { preventDefault: () => {} };
        await this.handleSearch(fakeEvent);
    }

    async performSearch(params) {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 模拟搜索结果
        return {
            query: params.query,
            total_time: 3.45,
            google_results: [
                {
                    title: `${params.query} - Reddit Discussion`,
                    url: `https://reddit.com/r/example/comments/123/${params.query.replace(/\s+/g, '_')}`,
                    snippet: `Great discussion about ${params.query} with lots of insights from the community.`
                }
            ],
            reddit_posts: [
                {
                    post: {
                        title: `Best practices for ${params.query}`,
                        author: 'example_user',
                        score: 156,
                        num_comments: 23
                    },
                    comments: [
                        {
                            author: 'helpful_user',
                            body: `I've been working with ${params.query} for years, here are my tips...`,
                            score: 45
                        }
                    ]
                }
            ],
            success: true
        };
    }

    setSearchLoading(loading) {
        const btnText = this.searchButton.querySelector('.btn-text');
        const btnLoading = this.searchButton.querySelector('.btn-loading');
        
        if (loading) {
            btnText.style.display = 'none';
            btnLoading.style.display = 'flex';
            this.searchButton.disabled = true;
            this.luckyButton.disabled = true;
        } else {
            btnText.style.display = 'block';
            btnLoading.style.display = 'none';
            this.searchButton.disabled = false;
            this.luckyButton.disabled = false;
        }
    }

    displayResults(results) {
        const html = `
            <div class="search-results">
                <div class="results-header">
                    <h3>搜索结果：${results.query}</h3>
                    <p class="results-meta">耗时 ${results.total_time} 秒</p>
                </div>
                
                <div class="results-content">
                    <div class="google-results">
                        <h4>Google 搜索结果</h4>
                        ${results.google_results.map(result => `
                            <div class="result-item">
                                <h5><a href="${result.url}" target="_blank">${result.title}</a></h5>
                                <p>${result.snippet}</p>
                                <span class="result-url">${result.url}</span>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div class="reddit-results">
                        <h4>Reddit 帖子分析</h4>
                        ${results.reddit_posts.map(post => `
                            <div class="post-item">
                                <h5>${post.post.title}</h5>
                                <div class="post-meta">
                                    作者: ${post.post.author} | 
                                    分数: ${post.post.score} | 
                                    评论: ${post.post.num_comments}
                                </div>
                                <div class="comments">
                                    ${post.comments.map(comment => `
                                        <div class="comment-item">
                                            <strong>${comment.author}</strong> (${comment.score}分): 
                                            ${comment.body}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
        
        this.resultsContent.innerHTML = html;
        this.showModal();
    }

    showError(message) {
        this.resultsContent.innerHTML = `
            <div class="error-message">
                <h3>搜索出错</h3>
                <p>${message}</p>
            </div>
        `;
        this.showModal();
    }

    showModal() {
        this.resultsModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        this.resultsModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new CogBridgesSearch();
});
