{"timestamp": "2025-07-20T17:18:45.058706", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1h8ox94", "subreddit": "singularity", "title": "How does <PERSON>, Grok or Llama compare to GPT or C", "score": 17, "num_comments": 13}, "timestamp": "2025-07-20T17:18:45.058705"}}
{"timestamp": "2025-07-20T17:18:45.264673", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1ih0eia", "subreddit": "Bard", "title": "<PERSON> vs <PERSON> vs <PERSON>tGP<PERSON> vs Deepseek: Who is Ac", "score": 45, "num_comments": 70}, "timestamp": "2025-07-20T17:18:45.264672"}}
{"timestamp": "2025-07-20T17:18:46.071387", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1lb0sdv", "subreddit": "grok", "title": "Should I subscribe for chatGPT or Grok?", "score": 15, "num_comments": 50}, "timestamp": "2025-07-20T17:18:46.070372"}}
{"timestamp": "2025-07-20T17:18:46.575301", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1h8ox94", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:18:46.575300"}}
{"timestamp": "2025-07-20T17:18:47.298148", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dqj1lg", "subreddit": "ClaudeAI", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspec", "score": 224, "num_comments": 139}, "timestamp": "2025-07-20T17:18:47.298148"}}
{"timestamp": "2025-07-20T17:18:47.788042", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1ih0eia", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:18:47.788042"}}
{"timestamp": "2025-07-20T17:18:47.983040", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1jnl8pn", "subreddit": "ClaudeAI", "title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ", "score": 548, "num_comments": 161}, "timestamp": "2025-07-20T17:18:47.983040"}}
{"timestamp": "2025-07-20T17:18:48.200176", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1lb0sdv", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:18:48.200176"}}
{"timestamp": "2025-07-20T17:18:50.088396", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dqj1lg", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:18:50.081845"}}
{"timestamp": "2025-07-20T17:18:51.125676", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1jnl8pn", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:18:51.125676"}}
{"timestamp": "2025-07-20T17:18:53.009144", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Sad_Run_9798", "subreddit": null, "posts_count": 0, "comments_count": 0, "subreddit_activity": 0}, "timestamp": "2025-07-20T17:18:53.009144"}}
{"timestamp": "2025-07-20T17:18:54.695431", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "irukadesune", "subreddit": null, "posts_count": 0, "comments_count": 0, "subreddit_activity": 0}, "timestamp": "2025-07-20T17:18:54.695431"}}
{"timestamp": "2025-07-20T17:20:51.932175", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1h8ox94", "subreddit": "singularity", "title": "How does Gemini, Grok or Llama compare to GPT or C", "score": 18, "num_comments": 13}, "timestamp": "2025-07-20T17:20:51.932175"}}
{"timestamp": "2025-07-20T17:20:52.238675", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1ih0eia", "subreddit": "Bard", "title": "Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac", "score": 45, "num_comments": 70}, "timestamp": "2025-07-20T17:20:52.238675"}}
{"timestamp": "2025-07-20T17:20:54.346463", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1lb0sdv", "subreddit": "grok", "title": "Should I subscribe for chatGPT or Grok?", "score": 16, "num_comments": 50}, "timestamp": "2025-07-20T17:20:54.346463"}}
{"timestamp": "2025-07-20T17:20:57.969772", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1jnl8pn", "subreddit": "ClaudeAI", "title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ", "score": 555, "num_comments": 161}, "timestamp": "2025-07-20T17:20:57.969771"}}
{"timestamp": "2025-07-20T17:20:58.867754", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dqj1lg", "subreddit": "ClaudeAI", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspec", "score": 228, "num_comments": 139}, "timestamp": "2025-07-20T17:20:58.867754"}}
{"timestamp": "2025-07-20T17:21:06.601348", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1h8ox94", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:21:06.601347"}}
{"timestamp": "2025-07-20T17:21:09.798163", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1ih0eia", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:21:09.798162"}}
{"timestamp": "2025-07-20T17:21:10.215856", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1lb0sdv", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:21:10.215856"}}
{"timestamp": "2025-07-20T17:21:13.878888", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1jnl8pn", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:21:13.878358"}}
{"timestamp": "2025-07-20T17:21:18.946729", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dqj1lg", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:21:18.946729"}}
