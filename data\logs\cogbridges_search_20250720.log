2025-07-20 16:43:25 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 16:43:25 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 16:43:25 - api.Replicate - INFO - 开始调用 Replicate API: POST run
2025-07-20 16:43:25 - api.Replicate - INFO - Replicate API调用成功，耗时: 0.00秒
2025-07-20 16:43:25 - services.llm_service - ERROR - Replicate API连接测试失败: asyncio.run() cannot be called from a running event loop
2025-07-20 16:43:26 - services.subreddit_classifier - INFO - 开始分类 20 个子版块
2025-07-20 16:43:26 - api.Replicate - INFO - 开始调用 Replicate API: POST run
2025-07-20 16:43:26 - api.Replicate - INFO - Replicate API调用成功，耗时: 0.00秒
2025-07-20 16:43:29 - services.llm_service - INFO - LLM调用成功，输出长度: 212
2025-07-20 16:43:29 - services.subreddit_classifier - INFO - 分类完成: 4 个类别
2025-07-20 16:43:29 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 16:43:31 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 16:43:31 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:43:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:43:31 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:43:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:43:31 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-20 16:43:35 - services.reddit_service - INFO - 获取用户历史成功 spez: 11帖子, 59评论
2025-07-20 16:43:35 - services.reddit_service - INFO - 从用户 spez 历史中提取到 10 个子版块
2025-07-20 16:43:35 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 16:43:35 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 16:43:35 - services.reddit_service - INFO - 开始并行提取 2 个用户的子版块
2025-07-20 16:43:35 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:43:35 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:43:35 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:43:35 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:43:35 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-20 16:43:35 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:43:35 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:43:35 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:43:35 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:43:40 - services.reddit_service - INFO - 获取用户历史成功 spez: 11帖子, 59评论
2025-07-20 16:43:40 - services.reddit_service - INFO - 从用户 spez 历史中提取到 10 个子版块
2025-07-20 16:44:07 - services.reddit_service - INFO - 获取用户历史成功 kn0thing: 130帖子, 840评论
2025-07-20 16:44:07 - services.reddit_service - INFO - 从用户 kn0thing 历史中提取到 163 个子版块
2025-07-20 16:44:07 - services.reddit_service - INFO - 子版块提取完成: 总共 170 个不同的子版块
2025-07-20 16:44:07 - services.reddit_service - INFO - 合并后共有 170 个不同的子版块
2025-07-20 16:44:07 - services.subreddit_classifier - INFO - 开始分类 20 个子版块
2025-07-20 16:44:07 - api.Replicate - INFO - 开始调用 Replicate API: POST run
2025-07-20 16:44:07 - api.Replicate - INFO - Replicate API调用成功，耗时: 0.00秒
2025-07-20 16:44:10 - services.llm_service - INFO - LLM调用成功，输出长度: 316
2025-07-20 16:44:10 - services.subreddit_classifier - INFO - 分类完成: 5 个类别
2025-07-20 16:44:10 - services.reddit_service - INFO - 并行获取 2 个用户的分类历史数据
2025-07-20 16:44:10 - services.reddit_service - INFO - 按分类子版块获取用户 spez 的历史数据
2025-07-20 16:44:10 - services.reddit_service - INFO - 处理类别 '教育类': 4 个子版块
2025-07-20 16:44:10 - services.reddit_service - INFO - 按分类子版块获取用户 kn0thing 的历史数据
2025-07-20 16:44:10 - services.reddit_service - INFO - 处理类别 '教育类': 4 个子版块
2025-07-20 16:44:11 - services.reddit_service - INFO - 获取用户 kn0thing 在 ASU 的评论: 0 条
2025-07-20 16:44:11 - services.reddit_service - INFO - 获取用户 spez 在 ASU 的评论: 0 条
2025-07-20 16:44:11 - services.reddit_service - INFO - 获取用户 kn0thing 在 ASU 的帖子: 0 条
2025-07-20 16:44:11 - services.reddit_service - INFO - 获取用户 spez 在 ASU 的帖子: 0 条
2025-07-20 16:44:12 - services.reddit_service - INFO - 获取用户 kn0thing 在 BinghamtonUniversity 的评论: 0 条
2025-07-20 16:44:12 - services.reddit_service - INFO - 获取用户 spez 在 BinghamtonUniversity 的评论: 0 条
2025-07-20 16:44:13 - services.reddit_service - INFO - 获取用户 kn0thing 在 BinghamtonUniversity 的帖子: 0 条
2025-07-20 16:44:13 - services.reddit_service - INFO - 获取用户 spez 在 BinghamtonUniversity 的帖子: 0 条
2025-07-20 16:44:14 - services.reddit_service - INFO - 获取用户 spez 在 Caltech 的评论: 0 条
2025-07-20 16:44:15 - services.reddit_service - INFO - 获取用户 kn0thing 在 Caltech 的评论: 0 条
2025-07-20 16:44:15 - services.reddit_service - INFO - 获取用户 spez 在 Caltech 的帖子: 0 条
2025-07-20 16:44:16 - services.reddit_service - INFO - 获取用户 spez 在 Cornell 的评论: 0 条
2025-07-20 16:44:16 - services.reddit_service - INFO - 获取用户 spez 在 Cornell 的帖子: 0 条
2025-07-20 16:44:16 - services.reddit_service - INFO - 类别 '教育类' 完成: 0 帖子, 0 评论
2025-07-20 16:44:16 - services.reddit_service - INFO - 处理类别 '问答类': 4 个子版块
2025-07-20 16:44:16 - services.reddit_service - INFO - 获取用户 kn0thing 在 Caltech 的帖子: 0 条
2025-07-20 16:44:17 - services.reddit_service - INFO - 获取用户 spez 在 AskHistorians 的评论: 0 条
2025-07-20 16:44:18 - services.reddit_service - INFO - 获取用户 kn0thing 在 Cornell 的评论: 0 条
2025-07-20 16:44:18 - services.reddit_service - INFO - 获取用户 spez 在 AskHistorians 的帖子: 0 条
2025-07-20 16:44:19 - services.reddit_service - INFO - 获取用户 spez 在 AskReddit 的评论: 2 条
2025-07-20 16:44:19 - services.reddit_service - INFO - 获取用户 kn0thing 在 Cornell 的帖子: 0 条
2025-07-20 16:44:19 - services.reddit_service - INFO - 类别 '教育类' 完成: 0 帖子, 0 评论
2025-07-20 16:44:19 - services.reddit_service - INFO - 处理类别 '问答类': 4 个子版块
2025-07-20 16:44:20 - services.reddit_service - INFO - 获取用户 spez 在 AskReddit 的帖子: 0 条
2025-07-20 16:44:20 - services.reddit_service - INFO - 获取用户 kn0thing 在 AskHistorians 的评论: 0 条
2025-07-20 16:44:21 - services.reddit_service - INFO - 获取用户 spez 在 AskWomen 的评论: 0 条
2025-07-20 16:44:21 - services.reddit_service - INFO - 获取用户 spez 在 AskWomen 的帖子: 0 条
2025-07-20 16:44:22 - services.reddit_service - INFO - 获取用户 kn0thing 在 AskHistorians 的帖子: 0 条
2025-07-20 16:44:22 - services.reddit_service - INFO - 获取用户 spez 在 DearYishan 的评论: 0 条
2025-07-20 16:44:23 - services.reddit_service - INFO - 获取用户 kn0thing 在 AskReddit 的评论: 0 条
2025-07-20 16:44:23 - services.reddit_service - INFO - 获取用户 spez 在 DearYishan 的帖子: 0 条
2025-07-20 16:44:23 - services.reddit_service - INFO - 类别 '问答类' 完成: 0 帖子, 2 评论
2025-07-20 16:44:23 - services.reddit_service - INFO - 处理类别 '加密货币类': 2 个子版块
2025-07-20 16:44:24 - services.reddit_service - INFO - 获取用户 spez 在 Bitcoin 的评论: 0 条
2025-07-20 16:44:24 - services.reddit_service - INFO - 获取用户 kn0thing 在 AskReddit 的帖子: 0 条
2025-07-20 16:44:25 - services.reddit_service - INFO - 获取用户 spez 在 Bitcoin 的帖子: 0 条
2025-07-20 16:44:25 - services.reddit_service - INFO - 获取用户 spez 在 CryptoCurrency 的评论: 0 条
2025-07-20 16:44:25 - services.reddit_service - INFO - 获取用户 kn0thing 在 AskWomen 的评论: 0 条
2025-07-20 16:44:26 - services.reddit_service - INFO - 获取用户 spez 在 CryptoCurrency 的帖子: 0 条
2025-07-20 16:44:26 - services.reddit_service - INFO - 类别 '加密货币类' 完成: 0 帖子, 0 评论
2025-07-20 16:44:26 - services.reddit_service - INFO - 处理类别 '体育类': 4 个子版块
2025-07-20 16:44:27 - services.reddit_service - INFO - 获取用户 kn0thing 在 AskWomen 的帖子: 0 条
2025-07-20 16:44:27 - services.reddit_service - INFO - 获取用户 spez 在 BostonBruins 的评论: 0 条
2025-07-20 16:45:34 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 16:45:34 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 16:45:34 - api.Replicate - INFO - 开始调用 Replicate API: POST run
2025-07-20 16:45:34 - api.Replicate - INFO - Replicate API调用成功，耗时: 0.00秒
2025-07-20 16:45:36 - services.llm_service - INFO - LLM调用成功，输出长度: 53
2025-07-20 16:45:36 - services.subreddit_classifier - INFO - 开始分类 12 个子版块
2025-07-20 16:45:36 - api.Replicate - INFO - 开始调用 Replicate API: POST run
2025-07-20 16:45:36 - api.Replicate - INFO - Replicate API调用成功，耗时: 0.00秒
2025-07-20 16:45:39 - services.llm_service - INFO - LLM调用成功，输出长度: 132
2025-07-20 16:45:39 - services.subreddit_classifier - INFO - 分类完成: 3 个类别
2025-07-20 16:45:39 - services.subreddit_classifier - INFO - 开始分类 6 个子版块
2025-07-20 16:45:39 - api.Replicate - INFO - 开始调用 Replicate API: POST run
2025-07-20 16:45:39 - api.Replicate - INFO - Replicate API调用成功，耗时: 0.00秒
2025-07-20 16:45:40 - services.llm_service - INFO - LLM调用成功，输出长度: 72
2025-07-20 16:45:40 - services.subreddit_classifier - INFO - 分类完成: 2 个类别
2025-07-20 16:45:40 - services.subreddit_classifier - INFO - 开始分类 6 个子版块
2025-07-20 16:45:40 - api.Replicate - INFO - 开始调用 Replicate API: POST run
2025-07-20 16:45:40 - api.Replicate - INFO - Replicate API调用成功，耗时: 0.00秒
2025-07-20 16:45:42 - services.llm_service - INFO - LLM调用成功，输出长度: 66
2025-07-20 16:45:42 - services.subreddit_classifier - INFO - 分类完成: 3 个类别
2025-07-20 16:45:42 - services.subreddit_classifier - INFO - 开始分类 7 个子版块
2025-07-20 16:45:42 - api.Replicate - INFO - 开始调用 Replicate API: POST run
2025-07-20 16:45:42 - api.Replicate - INFO - Replicate API调用成功，耗时: 0.00秒
2025-07-20 16:45:43 - services.llm_service - INFO - LLM调用成功，输出长度: 84
2025-07-20 16:45:43 - services.subreddit_classifier - INFO - 分类完成: 4 个类别
2025-07-20 16:55:29 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 16:55:29 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 16:55:29 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 16:55:29 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 16:55:29 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 16:55:29 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 16:55:29 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 16:55:29 - services.cogbridges_service - INFO - CogBridges服务已关闭
2025-07-20 16:55:29 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 16:55:29 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 16:55:29 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 16:55:29 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 16:55:29 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 16:55:29 - services.cogbridges_service - INFO - 开始带子版块分类的CogBridges搜索流程: Should I learn Python or JavaScript?
2025-07-20 16:55:29 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I learn Python or JavaScript?
2025-07-20 16:55:29 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 16:55:29 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I learn Python or JavaScript?
2025-07-20 16:55:29 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 16:55:31 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 430000），耗时 2.23秒
2025-07-20 16:55:31 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 2.24秒
2025-07-20 16:55:31 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 2.24秒
2025-07-20 16:55:31 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-20 16:55:31 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 16:55:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:31 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-20 16:55:31 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 16:55:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:31 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 16:55:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:31 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 16:55:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:31 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 16:55:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:35 - services.reddit_service - INFO - 获取帖子成功: Learn Python or JavaScript first?...
2025-07-20 16:55:35 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 16:55:35 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:35 - services.reddit_service - INFO - 获取帖子成功: What should I learn Python or JavaScript ...
2025-07-20 16:55:35 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 16:55:35 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:36 - services.reddit_service - INFO - 获取帖子成功: Learning Javascript after Python...
2025-07-20 16:55:36 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 16:55:36 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:37 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 16:55:37 - services.reddit_service - INFO - 获取帖子成功: Is it okay to learn Python and Javascript at the s...
2025-07-20 16:55:37 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 16:55:37 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:37 - services.reddit_service - INFO - 获取帖子成功: Should I learn JavaScript or Python as a beginner...
2025-07-20 16:55:37 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 16:55:37 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:38 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 16:55:38 - services.reddit_service - INFO - 获取评论成功: 5 条评论
2025-07-20 16:55:39 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 16:55:39 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 16:55:39 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 7.68秒
2025-07-20 16:55:39 - services.cogbridges_service - INFO - 步骤3: 提取评论者和子版块信息
2025-07-20 16:55:39 - services.cogbridges_service - INFO - 收集到 25 个评论者
2025-07-20 16:55:39 - services.reddit_service - INFO - 开始并行提取 25 个用户的子版块
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:39 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:39 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:40 - services.reddit_service - WARNING - 获取用户信息失败 nirvashprototype: 'Redditor' object has no attribute 'created_utc'
2025-07-20 16:55:40 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:40 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:40 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:40 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:40 - services.reddit_service - WARNING - 获取用户信息失败 SPACE_SHAMAN: 'Redditor' object has no attribute 'created_utc'
2025-07-20 16:55:40 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 16:55:40 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:55:40 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 16:55:40 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 16:56:15 - services.cogbridges_service - INFO - CogBridges服务已关闭
2025-07-20 16:56:15 - services.reddit_service - WARNING - 获取用户评论失败 BinnyBit: error with request Session is closed
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 BinnyBit: 494帖子, 100评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 BinnyBit 历史中提取到 86 个子版块
2025-07-20 16:56:15 - services.reddit_service - WARNING - 获取用户评论失败 jamescodesthings: error with request Session is closed
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 jamescodesthings: 37帖子, 99评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 jamescodesthings 历史中提取到 36 个子版块
2025-07-20 16:56:15 - services.reddit_service - WARNING - 获取用户评论失败 actuallyalys: error with request Session is closed
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 actuallyalys: 5帖子, 99评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 actuallyalys 历史中提取到 20 个子版块
2025-07-20 16:56:15 - services.reddit_service - WARNING - 获取用户评论失败 ingframin: error with request Session is closed
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 ingframin: 30帖子, 97评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 ingframin 历史中提取到 59 个子版块
2025-07-20 16:56:15 - services.reddit_service - WARNING - 获取用户评论失败 Xenofan23: error with request Session is closed
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 Xenofan23: 42帖子, 100评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 Xenofan23 历史中提取到 38 个子版块
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 demvo2: 1帖子, 26评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 demvo2 历史中提取到 6 个子版块
2025-07-20 16:56:15 - services.reddit_service - WARNING - 获取用户评论失败 Electrical-Ruin-8023: error with request Session is closed
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 Electrical-Ruin-8023: 0帖子, 99评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 Electrical-Ruin-8023 历史中提取到 4 个子版块
2025-07-20 16:56:15 - services.reddit_service - WARNING - 获取用户评论失败 Zukarukite: error with request Session is closed
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 Zukarukite: 30帖子, 97评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 Zukarukite 历史中提取到 59 个子版块
2025-07-20 16:56:15 - services.reddit_service - WARNING - 获取用户评论失败 _memelorddotjpeg_: error with request Session is closed
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 _memelorddotjpeg_: 90帖子, 86评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 _memelorddotjpeg_ 历史中提取到 77 个子版块
2025-07-20 16:56:15 - services.reddit_service - WARNING - 获取用户评论失败 Zigglie: error with request Session is closed
2025-07-20 16:56:15 - services.reddit_service - INFO - 获取用户历史成功 Zigglie: 4帖子, 99评论
2025-07-20 16:56:15 - services.reddit_service - INFO - 从用户 Zigglie 历史中提取到 12 个子版块
2025-07-20 17:02:57 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:02:57 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 17:02:57 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:02:57 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:02:57 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:02:57 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:02:57 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:02:57 - services.cogbridges_service - INFO - 开始带子版块分类的CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:02:57 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:02:57 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:02:57 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:02:57 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:02:58 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34700），耗时 0.82秒
2025-07-20 17:02:58 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.82秒
2025-07-20 17:02:58 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 0.82秒
2025-07-20 17:02:58 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-20 17:02:58 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:02:58 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:02:58 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-20 17:02:58 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:02:58 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:02:58 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:02:58 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:02:58 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:02:58 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:02:58 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:02:58 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:01 - services.reddit_service - INFO - 获取帖子成功: Should I subscribe for chatGPT or Grok?...
2025-07-20 17:03:01 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:03:01 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:01 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-20 17:03:01 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:03:01 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:03 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:03:03 - services.reddit_service - INFO - 获取帖子成功: Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac...
2025-07-20 17:03:03 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:03:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:03 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-20 17:03:03 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:03:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:03 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:03:03 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-20 17:03:03 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:03:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:05 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:03:06 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:03:06 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:03:06 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 8.11秒
2025-07-20 17:03:06 - services.cogbridges_service - INFO - 步骤3: 提取评论者和子版块信息
2025-07-20 17:03:06 - services.cogbridges_service - INFO - 收集到 28 个评论者
2025-07-20 17:03:06 - services.reddit_service - INFO - 开始并行提取 28 个用户的子版块
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:06 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:06 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:08 - services.reddit_service - INFO - 获取用户历史成功 irukadesune: 0帖子, 0评论
2025-07-20 17:03:08 - services.reddit_service - INFO - 从用户 irukadesune 历史中提取到 0 个子版块
2025-07-20 17:03:08 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:08 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:08 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:08 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:10 - services.reddit_service - INFO - 获取用户历史成功 Sad_Run_9798: 0帖子, 0评论
2025-07-20 17:03:10 - services.reddit_service - INFO - 从用户 Sad_Run_9798 历史中提取到 0 个子版块
2025-07-20 17:03:10 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:03:10 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:10 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:03:10 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:03:41 - services.cogbridges_service - INFO - CogBridges服务已关闭
2025-07-20 17:03:41 - services.reddit_service - WARNING - 获取用户评论失败 Cagnazzo82: error with request Session is closed
2025-07-20 17:03:41 - services.reddit_service - INFO - 获取用户历史成功 Cagnazzo82: 31帖子, 100评论
2025-07-20 17:03:41 - services.reddit_service - INFO - 从用户 Cagnazzo82 历史中提取到 19 个子版块
2025-07-20 17:03:41 - services.reddit_service - WARNING - 获取用户评论失败 devonschmidt: error with request Session is closed
2025-07-20 17:03:41 - services.reddit_service - INFO - 获取用户历史成功 devonschmidt: 45帖子, 98评论
2025-07-20 17:03:41 - services.reddit_service - INFO - 从用户 devonschmidt 历史中提取到 30 个子版块
2025-07-20 17:03:41 - services.reddit_service - WARNING - 获取用户评论失败 Solarka45: error with request Session is closed
2025-07-20 17:03:41 - services.reddit_service - INFO - 获取用户历史成功 Solarka45: 44帖子, 100评论
2025-07-20 17:03:41 - services.reddit_service - INFO - 从用户 Solarka45 历史中提取到 45 个子版块
2025-07-20 17:03:41 - services.reddit_service - WARNING - 获取用户评论失败 Kathane37: error with request Session is closed
2025-07-20 17:03:41 - services.reddit_service - INFO - 获取用户历史成功 Kathane37: 21帖子, 98评论
2025-07-20 17:03:41 - services.reddit_service - INFO - 从用户 Kathane37 历史中提取到 30 个子版块
2025-07-20 17:03:41 - services.reddit_service - WARNING - 获取用户评论失败 Heavy_Hunt7860: error with request Session is closed
2025-07-20 17:03:41 - services.reddit_service - INFO - 获取用户历史成功 Heavy_Hunt7860: 31帖子, 100评论
2025-07-20 17:03:41 - services.reddit_service - INFO - 从用户 Heavy_Hunt7860 历史中提取到 40 个子版块
2025-07-20 17:03:41 - services.reddit_service - WARNING - 获取用户评论失败 OfficeSalamander: error with request Session is closed
2025-07-20 17:03:41 - services.reddit_service - INFO - 获取用户历史成功 OfficeSalamander: 32帖子, 99评论
2025-07-20 17:03:41 - services.reddit_service - INFO - 从用户 OfficeSalamander 历史中提取到 44 个子版块
2025-07-20 17:03:41 - services.reddit_service - WARNING - 获取用户评论失败 MrBietola: error with request Session is closed
2025-07-20 17:03:41 - services.reddit_service - INFO - 获取用户历史成功 MrBietola: 42帖子, 99评论
2025-07-20 17:03:41 - services.reddit_service - INFO - 从用户 MrBietola 历史中提取到 20 个子版块
2025-07-20 17:03:42 - services.reddit_service - WARNING - 获取用户评论失败 focal-fossa: error with request Session is closed
2025-07-20 17:03:42 - services.reddit_service - INFO - 获取用户历史成功 focal-fossa: 2帖子, 96评论
2025-07-20 17:03:42 - services.reddit_service - INFO - 从用户 focal-fossa 历史中提取到 14 个子版块
2025-07-20 17:03:42 - services.reddit_service - WARNING - 获取用户评论失败 MarinatedTechnician: error with request Session is closed
2025-07-20 17:03:42 - services.reddit_service - INFO - 获取用户历史成功 MarinatedTechnician: 36帖子, 100评论
2025-07-20 17:03:42 - services.reddit_service - INFO - 从用户 MarinatedTechnician 历史中提取到 46 个子版块
2025-07-20 17:04:12 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:04:12 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 17:04:12 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:04:12 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:04:12 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:04:12 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:04:12 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:04:12 - services.cogbridges_service - INFO - 开始带子版块分类的CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:04:12 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:04:12 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:04:12 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:04:12 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:04:12 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34700），耗时 0.83秒
2025-07-20 17:04:12 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.83秒
2025-07-20 17:04:12 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 0.83秒
2025-07-20 17:04:12 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-20 17:04:12 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:04:12 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:12 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-20 17:04:12 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:04:12 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:12 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:04:12 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:12 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:04:12 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:12 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:04:12 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:15 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-20 17:04:15 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:04:15 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:16 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:04:17 - services.reddit_service - INFO - 获取帖子成功: Should I subscribe for chatGPT or Grok?...
2025-07-20 17:04:17 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:04:17 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:17 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-20 17:04:17 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:04:17 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:17 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-20 17:04:17 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:04:17 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:18 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:04:20 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:04:21 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:04:21 - services.reddit_service - INFO - 获取帖子成功: Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac...
2025-07-20 17:04:21 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:04:21 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:04:23 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 10.34秒
2025-07-20 17:04:23 - services.cogbridges_service - INFO - 步骤3: 提取评论者和子版块信息
2025-07-20 17:04:23 - services.cogbridges_service - INFO - 收集到 28 个评论者
2025-07-20 17:04:23 - services.reddit_service - INFO - 开始并行提取 28 个用户的子版块
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:23 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:24 - services.reddit_service - INFO - 获取用户历史成功 irukadesune: 0帖子, 0评论
2025-07-20 17:04:24 - services.reddit_service - INFO - 从用户 irukadesune 历史中提取到 0 个子版块
2025-07-20 17:04:24 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:24 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:24 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:24 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:24 - services.reddit_service - INFO - 获取用户历史成功 Sad_Run_9798: 0帖子, 0评论
2025-07-20 17:04:24 - services.reddit_service - INFO - 从用户 Sad_Run_9798 历史中提取到 0 个子版块
2025-07-20 17:04:24 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:24 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:24 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:24 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:46 - services.reddit_service - INFO - 获取用户历史成功 Spiritual-Extent1105: 2帖子, 7评论
2025-07-20 17:04:46 - services.reddit_service - INFO - 从用户 Spiritual-Extent1105 历史中提取到 6 个子版块
2025-07-20 17:04:46 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:46 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.01秒
2025-07-20 17:04:46 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:46 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:51 - services.reddit_service - WARNING - 获取用户信息失败 Jean-Porte: 'Redditor' object has no attribute 'created_utc'
2025-07-20 17:04:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:04:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:04:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:04:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:12 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:06:12 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:06:12 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:06:12 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:06:12 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:06:13 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34700），耗时 1.24秒
2025-07-20 17:06:13 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.25秒
2025-07-20 17:06:13 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 1.25秒
2025-07-20 17:06:13 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-20 17:06:13 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:06:13 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:13 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:06:13 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:13 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:06:13 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:13 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:06:13 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:13 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:06:13 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:22 - services.reddit_service - INFO - 获取帖子成功: Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac...
2025-07-20 17:06:22 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:06:22 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:23 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-20 17:06:23 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:06:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:25 - services.reddit_service - INFO - 获取帖子成功: Should I subscribe for chatGPT or Grok?...
2025-07-20 17:06:25 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:06:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:26 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-20 17:06:26 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:06:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:28 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-20 17:06:28 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:06:28 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:06:39 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:06:40 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:06:42 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:06:44 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:06:46 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:06:46 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 33.64秒
2025-07-20 17:06:46 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-20 17:06:46 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-20 17:06:56 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-20 17:06:56 - services.reddit_service - WARNING - 用户 Jean-Porte 的评论访问被禁止 (403): received 403 HTTP response
2025-07-20 17:06:56 - services.reddit_service - INFO - 获取用户 irukadesune 在 grok 的评论: 0 条
2025-07-20 17:06:56 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-20 17:06:56 - services.reddit_service - INFO - 获取用户 thereisonlythedance 在 Bard 的评论: 0 条
2025-07-20 17:06:57 - services.reddit_service - INFO - 获取用户 Solarka45 在 Bard 的评论: 0 条
2025-07-20 17:06:57 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-20 17:06:57 - services.reddit_service - INFO - 获取用户 BlackStarCorona 在 grok 的评论: 0 条
2025-07-20 17:06:57 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-20 17:06:58 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-20 17:06:58 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-20 17:06:58 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-20 17:06:58 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-20 17:07:03 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-20 17:07:04 - services.reddit_service - INFO - 获取用户 thereisonlythedance 在 Bard 的帖子: 0 条
2025-07-20 17:07:04 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:07:06 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:07:06 - services.reddit_service - WARNING - 用户 Jean-Porte 的帖子访问被禁止 (403): received 403 HTTP response
2025-07-20 17:07:07 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-20 17:07:08 - services.reddit_service - INFO - 获取用户 Solarka45 在 Bard 的帖子: 1 条
2025-07-20 17:07:08 - services.reddit_service - INFO - 获取用户 irukadesune 在 grok 的帖子: 0 条
2025-07-20 17:07:08 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-20 17:07:08 - services.reddit_service - INFO - 获取用户 BlackStarCorona 在 grok 的帖子: 0 条
2025-07-20 17:07:08 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:07:08 - services.reddit_service - INFO - 获取用户 g_bleezy 在 grok 的评论: 1 条
2025-07-20 17:07:08 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:07:09 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的帖子: 1 条
2025-07-20 17:07:09 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的帖子: 1 条
2025-07-20 17:07:09 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:07:12 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-20 17:07:12 - services.reddit_service - INFO - 获取用户 Aggravating_Scratch9 在 grok 的评论: 1 条
2025-07-20 17:07:22 - services.reddit_service - INFO - 获取用户 awaggoner 在 grok 的评论: 2 条
2025-07-20 17:07:22 - services.reddit_service - INFO - 获取用户 Spiritual-Extent1105 在 Bard 的评论: 2 条
2025-07-20 17:07:22 - services.reddit_service - INFO - 获取用户 Aggravating_Scratch9 在 grok 的帖子: 0 条
2025-07-20 17:07:23 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:07:29 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-20 17:07:29 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-20 17:08:17 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:08:17 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 17:08:17 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:08:17 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:08:17 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:08:17 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:08:17 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:08:17 - services.cogbridges_service - INFO - 开始带子版块分类的CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:08:17 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:08:17 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:08:17 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:08:17 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:08:18 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34700），耗时 0.75秒
2025-07-20 17:08:18 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.75秒
2025-07-20 17:08:18 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 0.75秒
2025-07-20 17:08:18 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-20 17:08:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:08:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:18 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-20 17:08:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:08:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:08:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:08:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:08:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:21 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-20 17:08:21 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:08:21 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:21 - services.reddit_service - INFO - 获取帖子成功: Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac...
2025-07-20 17:08:21 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:08:21 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:22 - services.reddit_service - INFO - 获取帖子成功: Should I subscribe for chatGPT or Grok?...
2025-07-20 17:08:22 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:08:22 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:22 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-20 17:08:22 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:08:22 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:23 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:08:23 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:08:23 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-20 17:08:23 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:08:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:24 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:08:26 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:08:26 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:08:26 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 8.38秒
2025-07-20 17:08:26 - services.cogbridges_service - INFO - 步骤3: 提取评论者和子版块信息
2025-07-20 17:08:26 - services.cogbridges_service - INFO - 收集到 28 个评论者
2025-07-20 17:08:26 - services.reddit_service - INFO - 开始并行提取 28 个用户的子版块
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:26 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:26 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:27 - services.reddit_service - WARNING - 获取用户信息失败 Jean-Porte: 'Redditor' object has no attribute 'created_utc'
2025-07-20 17:08:27 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:27 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:27 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:27 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:28 - services.reddit_service - INFO - 获取用户历史成功 irukadesune: 0帖子, 0评论
2025-07-20 17:08:28 - services.reddit_service - INFO - 从用户 irukadesune 历史中提取到 0 个子版块
2025-07-20 17:08:28 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:28 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:28 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:28 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:34 - services.reddit_service - INFO - 获取用户历史成功 Sad_Run_9798: 0帖子, 0评论
2025-07-20 17:08:34 - services.reddit_service - INFO - 从用户 Sad_Run_9798 历史中提取到 0 个子版块
2025-07-20 17:08:34 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:08:34 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:08:34 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:08:34 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:17 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:10:17 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:10:17 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:10:17 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:10:17 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:10:18 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34700），耗时 0.83秒
2025-07-20 17:10:18 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.83秒
2025-07-20 17:10:18 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 0.83秒
2025-07-20 17:10:18 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-20 17:10:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:10:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:10:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:10:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:10:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:10:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:25 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-20 17:10:25 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:10:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:27 - services.reddit_service - INFO - 获取帖子成功: Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac...
2025-07-20 17:10:27 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:10:27 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:27 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-20 17:10:27 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:10:27 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:28 - services.reddit_service - INFO - 获取帖子成功: Should I subscribe for chatGPT or Grok?...
2025-07-20 17:10:28 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:10:28 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:33 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-20 17:10:33 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:10:33 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:10:39 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:10:43 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:10:44 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:10:45 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:10:49 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:10:49 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 31.03秒
2025-07-20 17:10:49 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-20 17:10:49 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-20 17:10:56 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-20 17:10:56 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-20 17:10:56 - services.reddit_service - WARNING - 用户 Jean-Porte 的评论访问被禁止 (403): received 403 HTTP response
2025-07-20 17:10:56 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-20 17:10:57 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-20 17:10:57 - services.reddit_service - INFO - 获取用户 irukadesune 在 grok 的评论: 0 条
2025-07-20 17:10:57 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-20 17:10:57 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-20 17:10:57 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-20 17:10:57 - services.reddit_service - INFO - 获取用户 BlackStarCorona 在 grok 的评论: 0 条
2025-07-20 17:10:57 - services.reddit_service - INFO - 获取用户 thereisonlythedance 在 Bard 的评论: 0 条
2025-07-20 17:10:57 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-20 17:10:58 - services.reddit_service - INFO - 获取用户 Solarka45 在 Bard 的评论: 0 条
2025-07-20 17:11:03 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-20 17:11:04 - services.reddit_service - INFO - 获取用户 irukadesune 在 grok 的帖子: 0 条
2025-07-20 17:11:04 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的帖子: 1 条
2025-07-20 17:11:04 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的帖子: 1 条
2025-07-20 17:11:04 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:11:05 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:11:07 - services.reddit_service - WARNING - 用户 Jean-Porte 的帖子访问被禁止 (403): received 403 HTTP response
2025-07-20 17:11:07 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:11:07 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-20 17:11:07 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-20 17:11:08 - services.reddit_service - INFO - 获取用户 thereisonlythedance 在 Bard 的帖子: 0 条
2025-07-20 17:11:08 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:11:08 - services.reddit_service - INFO - 获取用户 BlackStarCorona 在 grok 的帖子: 0 条
2025-07-20 17:11:08 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:11:08 - services.reddit_service - INFO - 获取用户 g_bleezy 在 grok 的评论: 1 条
2025-07-20 17:11:09 - services.reddit_service - INFO - 获取用户 Solarka45 在 Bard 的帖子: 1 条
2025-07-20 17:11:13 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的帖子: 1 条
2025-07-20 17:11:17 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-20 17:11:17 - services.reddit_service - INFO - 获取用户 Aggravating_Scratch9 在 grok 的评论: 1 条
2025-07-20 17:11:28 - services.reddit_service - INFO - 获取用户 Spiritual-Extent1105 在 Bard 的评论: 2 条
2025-07-20 17:11:28 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:11:28 - services.reddit_service - INFO - 获取用户 awaggoner 在 grok 的评论: 2 条
2025-07-20 17:11:29 - services.reddit_service - INFO - 获取用户 Aggravating_Scratch9 在 grok 的帖子: 0 条
2025-07-20 17:11:38 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-20 17:11:39 - services.reddit_service - INFO - 获取用户 Spiritual-Extent1105 在 Bard 的帖子: 0 条
2025-07-20 17:11:39 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-20 17:11:39 - services.reddit_service - INFO - 获取用户 awaggoner 在 grok 的帖子: 0 条
2025-07-20 17:11:49 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的帖子: 1 条
2025-07-20 17:11:50 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的帖子: 3 条
2025-07-20 17:12:56 - services.reddit_service - INFO - 获取用户 hyxon4 在 Bard 的评论: 6 条
2025-07-20 17:13:08 - services.reddit_service - INFO - 获取用户 hyxon4 在 Bard 的帖子: 2 条
2025-07-20 17:13:53 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-20 17:14:02 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的帖子: 5 条
2025-07-20 17:14:07 - services.reddit_service - INFO - 获取用户 Decoert 在 Bard 的评论: 14 条
2025-07-20 17:14:17 - services.reddit_service - INFO - 获取用户 Decoert 在 Bard 的帖子: 1 条
2025-07-20 17:14:55 - services.reddit_service - INFO - 获取用户 Gaiden206 在 Bard 的评论: 16 条
2025-07-20 17:14:58 - services.reddit_service - WARNING - 获取用户评论失败 Gaiden206: error with request Session is closed
2025-07-20 17:14:58 - services.reddit_service - ERROR - 获取用户历史失败 Gaiden206: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:14:58 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-20 17:14:58 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-20 17:14:58 - services.reddit_service - WARNING - 获取用户评论失败 hyxon4: error with request Session is closed
2025-07-20 17:14:58 - services.reddit_service - ERROR - 获取用户历史失败 hyxon4: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:14:58 - services.reddit_service - WARNING - 获取用户评论失败 Cagnazzo82: error with request Session is closed
2025-07-20 17:14:58 - services.reddit_service - ERROR - 获取用户历史失败 Cagnazzo82: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:14:58 - services.reddit_service - WARNING - 获取用户评论失败 g_bleezy: error with request Session is closed
2025-07-20 17:14:58 - services.reddit_service - ERROR - 获取用户历史失败 g_bleezy: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:14:58 - services.reddit_service - WARNING - 获取用户评论失败 awaggoner: error with request Session is closed
2025-07-20 17:14:58 - services.reddit_service - ERROR - 获取用户历史失败 awaggoner: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:14:59 - services.reddit_service - WARNING - 获取用户评论失败 Heavy_Hunt7860: error with request Session is closed
2025-07-20 17:14:59 - services.reddit_service - ERROR - 获取用户历史失败 Heavy_Hunt7860: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:14:59 - services.reddit_service - WARNING - 获取用户评论失败 haslo: error with request Session is closed
2025-07-20 17:14:59 - services.reddit_service - ERROR - 获取用户历史失败 haslo: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:14:59 - services.reddit_service - WARNING - 获取用户评论失败 MarinatedTechnician: error with request Session is closed
2025-07-20 17:14:59 - services.reddit_service - ERROR - 获取用户历史失败 MarinatedTechnician: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:14:59 - services.reddit_service - WARNING - 获取用户评论失败 focal-fossa: error with request Session is closed
2025-07-20 17:14:59 - services.reddit_service - ERROR - 获取用户历史失败 focal-fossa: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:17:15 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:17:15 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 17:17:15 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:17:15 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:17:15 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:17:15 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:17:15 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:17:15 - services.cogbridges_service - INFO - 开始带子版块分类的CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:17:15 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:17:15 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:17:15 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.00秒
2025-07-20 17:17:15 - services.cogbridges_service - ERROR - 带分类的CogBridges搜索失败: 'SearchQuery' object has no attribute 'extra_params'
2025-07-20 17:17:15 - services.cogbridges_service - INFO - CogBridges服务已关闭
2025-07-20 17:17:53 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:17:53 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 17:17:53 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:17:53 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:17:53 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:17:53 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:17:53 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:17:53 - services.cogbridges_service - INFO - 开始带子版块分类的CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:17:53 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:17:53 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:17:53 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.00秒
2025-07-20 17:17:53 - services.google_search_api - ERROR - 处理Google搜索结果时发生未知错误: 'Config' object has no attribute 'PROXY_ENABLED'
2025-07-20 17:17:53 - services.cogbridges_service - WARNING - Google搜索失败: 'Config' object has no attribute 'PROXY_ENABLED'
2025-07-20 17:17:53 - services.cogbridges_service - INFO - CogBridges服务已关闭
2025-07-20 17:18:18 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:18:18 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 17:18:18 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:18:18 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:18:18 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:18:18 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:18:18 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:18:18 - services.cogbridges_service - INFO - 开始带子版块分类的CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:18:18 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:18:18 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:18:18 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.00秒
2025-07-20 17:18:18 - utils.proxy_utils - INFO - 异步代理已禁用
2025-07-20 17:18:19 - services.google_search_api - ERROR - 处理Google搜索结果时发生未知错误: log_search_operation() missing 1 required positional argument: 'duration'
2025-07-20 17:18:19 - services.cogbridges_service - WARNING - Google搜索失败: log_search_operation() missing 1 required positional argument: 'duration'
2025-07-20 17:18:19 - services.cogbridges_service - INFO - CogBridges服务已关闭
2025-07-20 17:18:40 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:18:40 - services.llm_service - INFO - LLM服务初始化成功
2025-07-20 17:18:40 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:18:40 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:18:40 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:18:40 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:18:40 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:18:40 - services.cogbridges_service - INFO - 开始带子版块分类的CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:18:40 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:18:40 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:18:40 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.00秒
2025-07-20 17:18:40 - utils.proxy_utils - INFO - 异步代理已禁用
2025-07-20 17:18:41 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34700），耗时 1.20秒
2025-07-20 17:18:41 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 1.20秒
2025-07-20 17:18:41 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-20 17:18:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:18:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:41 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-20 17:18:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:18:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:18:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:18:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:18:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:45 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-20 17:18:45 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:18:45 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:45 - services.reddit_service - INFO - 获取帖子成功: Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac...
2025-07-20 17:18:45 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:18:45 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:46 - services.reddit_service - INFO - 获取帖子成功: Should I subscribe for chatGPT or Grok?...
2025-07-20 17:18:46 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:18:46 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:46 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:18:47 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-20 17:18:47 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:18:47 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:47 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:18:47 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-20 17:18:47 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:18:47 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:48 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:18:50 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:18:51 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:18:51 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 9.38秒
2025-07-20 17:18:51 - services.cogbridges_service - INFO - 步骤3: 提取评论者和子版块信息
2025-07-20 17:18:51 - services.cogbridges_service - INFO - 收集到 28 个评论者
2025-07-20 17:18:51 - services.reddit_service - INFO - 开始并行提取 28 个用户的子版块
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:51 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:52 - services.reddit_service - WARNING - 获取用户信息失败 Jean-Porte: 'Redditor' object has no attribute 'created_utc'
2025-07-20 17:18:52 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:52 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:52 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:52 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:53 - services.reddit_service - INFO - 获取用户历史成功 Sad_Run_9798: 0帖子, 0评论
2025-07-20 17:18:53 - services.reddit_service - INFO - 从用户 Sad_Run_9798 历史中提取到 0 个子版块
2025-07-20 17:18:53 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:53 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:53 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:53 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:54 - services.reddit_service - INFO - 获取用户历史成功 irukadesune: 0帖子, 0评论
2025-07-20 17:18:54 - services.reddit_service - INFO - 从用户 irukadesune 历史中提取到 0 个子版块
2025-07-20 17:18:54 - api.Reddit - INFO - 开始调用 Reddit API: GET user_history
2025-07-20 17:18:54 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:18:54 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-20 17:18:54 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:40 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:20:40 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-20 17:20:40 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-20 17:20:40 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.00秒
2025-07-20 17:20:41 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34700），耗时 1.24秒
2025-07-20 17:20:41 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 1.24秒
2025-07-20 17:20:41 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-20 17:20:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:20:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:20:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:20:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:20:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-20 17:20:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:51 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-20 17:20:51 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:20:51 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:52 - services.reddit_service - INFO - 获取帖子成功: Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac...
2025-07-20 17:20:52 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:20:52 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:54 - services.reddit_service - INFO - 获取帖子成功: Should I subscribe for chatGPT or Grok?...
2025-07-20 17:20:54 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:20:54 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:57 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-20 17:20:57 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:20:57 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:20:58 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-20 17:20:58 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-20 17:20:58 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-20 17:21:06 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:21:09 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:21:10 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:21:13 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:21:18 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-20 17:21:18 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 37.15秒
2025-07-20 17:21:18 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-20 17:21:18 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-20 17:21:25 - services.reddit_service - WARNING - 用户 Jean-Porte 的评论访问被禁止 (403): received 403 HTTP response
2025-07-20 17:21:25 - services.reddit_service - INFO - 获取用户 irukadesune 在 grok 的评论: 0 条
2025-07-20 17:21:26 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-20 17:21:26 - services.reddit_service - INFO - 获取用户 Solarka45 在 Bard 的评论: 0 条
2025-07-20 17:21:26 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-20 17:21:27 - services.reddit_service - INFO - 获取用户 thereisonlythedance 在 Bard 的评论: 0 条
2025-07-20 17:21:27 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-20 17:21:28 - services.reddit_service - INFO - 获取用户 BlackStarCorona 在 grok 的评论: 0 条
2025-07-20 17:21:28 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-20 17:21:28 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-20 17:21:28 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-20 17:21:28 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-20 17:21:28 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-20 17:21:33 - services.reddit_service - INFO - 获取用户 irukadesune 在 grok 的帖子: 0 条
2025-07-20 17:21:33 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-20 17:21:33 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:21:34 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:21:35 - services.reddit_service - INFO - 获取用户 Solarka45 在 Bard 的帖子: 1 条
2025-07-20 17:21:35 - services.reddit_service - WARNING - 用户 Jean-Porte 的帖子访问被禁止 (403): received 403 HTTP response
2025-07-20 17:21:37 - services.reddit_service - INFO - 获取用户 thereisonlythedance 在 Bard 的帖子: 0 条
2025-07-20 17:21:37 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-20 17:21:38 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的帖子: 1 条
2025-07-20 17:21:38 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:21:38 - services.reddit_service - INFO - 获取用户 BlackStarCorona 在 grok 的帖子: 0 条
2025-07-20 17:21:39 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-20 17:21:39 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:21:39 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:21:39 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的帖子: 1 条
2025-07-20 17:21:40 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-20 17:21:40 - services.reddit_service - INFO - 获取用户 Aggravating_Scratch9 在 grok 的评论: 1 条
2025-07-20 17:21:40 - services.reddit_service - INFO - 获取用户 g_bleezy 在 grok 的评论: 1 条
2025-07-20 17:21:51 - services.reddit_service - INFO - 获取用户 g_bleezy 在 grok 的帖子: 0 条
2025-07-20 17:21:51 - services.reddit_service - INFO - 获取用户 Spiritual-Extent1105 在 Bard 的评论: 2 条
2025-07-20 17:21:51 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-20 17:21:51 - services.reddit_service - INFO - 获取用户 awaggoner 在 grok 的评论: 2 条
2025-07-20 17:21:51 - services.reddit_service - INFO - 获取用户 Aggravating_Scratch9 在 grok 的帖子: 0 条
2025-07-20 17:22:01 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-20 17:22:02 - services.reddit_service - INFO - 获取用户 Spiritual-Extent1105 在 Bard 的帖子: 0 条
2025-07-20 17:22:02 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-20 17:22:02 - services.reddit_service - INFO - 获取用户 awaggoner 在 grok 的帖子: 0 条
2025-07-20 17:22:12 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的帖子: 1 条
2025-07-20 17:22:13 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的帖子: 3 条
2025-07-20 17:23:16 - services.reddit_service - INFO - 获取用户 hyxon4 在 Bard 的评论: 6 条
2025-07-20 17:23:28 - services.reddit_service - INFO - 获取用户 hyxon4 在 Bard 的帖子: 2 条
2025-07-20 17:23:49 - services.reddit_service - WARNING - 获取用户评论失败 OfficeSalamander: no running event loop
2025-07-20 17:23:49 - services.reddit_service - ERROR - 获取用户历史失败 OfficeSalamander: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:49 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-20 17:23:49 - services.reddit_service - WARNING - 获取用户评论失败 focal-fossa: no running event loop
2025-07-20 17:23:49 - services.reddit_service - ERROR - 获取用户历史失败 focal-fossa: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:49 - services.reddit_service - WARNING - 获取用户评论失败 Aggravating_Scratch9: no running event loop
2025-07-20 17:23:49 - services.reddit_service - ERROR - 获取用户历史失败 Aggravating_Scratch9: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:49 - services.reddit_service - WARNING - 获取用户评论失败 Cagnazzo82: no running event loop
2025-07-20 17:23:49 - services.reddit_service - ERROR - 获取用户历史失败 Cagnazzo82: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:49 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-20 17:23:49 - services.reddit_service - WARNING - 获取用户评论失败 xAragon_: no running event loop
2025-07-20 17:23:49 - services.reddit_service - ERROR - 获取用户历史失败 xAragon_: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:49 - services.reddit_service - WARNING - 获取用户评论失败 Heavy_Hunt7860: no running event loop
2025-07-20 17:23:49 - services.reddit_service - ERROR - 获取用户历史失败 Heavy_Hunt7860: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:49 - services.reddit_service - INFO - 获取用户 Decoert 在 Bard 的评论: 14 条
2025-07-20 17:23:49 - services.reddit_service - WARNING - 获取用户评论失败 hyxon4: no running event loop
2025-07-20 17:23:49 - services.reddit_service - ERROR - 获取用户历史失败 hyxon4: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:49 - services.reddit_service - INFO - 获取用户 Gaiden206 在 Bard 的评论: 16 条
2025-07-20 17:23:49 - services.reddit_service - WARNING - 获取用户评论失败 haslo: no running event loop
2025-07-20 17:23:49 - services.reddit_service - ERROR - 获取用户历史失败 haslo: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:50 - services.reddit_service - WARNING - 获取用户评论失败 MarinatedTechnician: no running event loop
2025-07-20 17:23:50 - services.reddit_service - ERROR - 获取用户历史失败 MarinatedTechnician: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:50 - services.reddit_service - WARNING - 获取用户评论失败 g_bleezy: no running event loop
2025-07-20 17:23:50 - services.reddit_service - ERROR - 获取用户历史失败 g_bleezy: sys.meta_path is None, Python is likely shutting down
2025-07-20 17:23:50 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-20 17:29:26 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:29:26 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:29:26 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:29:26 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:29:26 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:29:26 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:30:00 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:30:00 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:30:00 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:30:00 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:30:00 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:30:00 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:30:46 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:30:46 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:30:46 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:30:46 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:30:46 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:30:46 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:32:36 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:32:36 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:32:36 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:32:36 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:32:36 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:32:36 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:36:48 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:36:48 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:36:48 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:36:48 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:36:48 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:36:48 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:37:45 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:37:45 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:37:45 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:37:45 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:37:45 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:37:45 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:43:21 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:43:21 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:43:21 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:43:21 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:43:21 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:43:21 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:45:20 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:45:20 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:45:20 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:45:21 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:45:21 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:45:21 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:45:24 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:45:24 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:45:24 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:45:24 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:45:24 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:45:24 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:49:10 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:49:10 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:49:10 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:49:10 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:49:10 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:49:10 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:49:12 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:49:12 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:49:12 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:49:12 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:49:12 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:49:12 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:53:26 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:53:26 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:53:26 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:53:26 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:53:26 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:53:26 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-20 17:53:30 - services.llm_service - WARNING - Replicate API未配置，LLM服务不可用
2025-07-20 17:53:30 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-20 17:53:30 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-20 17:53:30 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-20 17:53:30 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-20 17:53:30 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
