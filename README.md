# CogBridges - Reddit智能搜索分析平台

<div align="center">

![CogBridges Logo](https://via.placeholder.com/200x80/4A90E2/FFFFFF?text=CogBridges)

**基于Google Custom Search API的Reddit数据智能分析平台**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.0+-green.svg)](https://flask.palletsprojects.com)
[![Google API](https://img.shields.io/badge/Google-Custom%20Search-red.svg)](https://developers.google.com/custom-search)
[![Reddit API](https://img.shields.io/badge/Reddit-API-orange.svg)](https://www.reddit.com/dev/api)

[快速开始](#-快速开始) • [技术架构](#-技术架构) • [API接口](#-api接口) • [测试验证](#-测试验证)

</div>

---

## 🎯 项目简介

**CogBridges** 是一个基于Google Custom Search API和Reddit API的智能搜索分析平台，实现了从Google搜索到Reddit数据获取再到用户画像分析的完整业务流程。

### 🌟 核心特性

- **🔍 Google Custom Search API集成**：使用官方API进行精准的Reddit内容搜索
- **📊 Reddit数据深度分析**：获取帖子内容、评论数据和用户历史信息
- **👥 用户画像构建**：分析评论者的发言历史，构建用户兴趣画像
- **⚡ 异步并发处理**：高效的数据获取和处理流程
- **🌐 Web界面**：现代化的前端界面，支持实时搜索和结果展示
- **📈 完整业务流程**：端到端的数据处理和分析流水线

---

## 🏗 技术架构

### 核心业务流程

```mermaid
graph TD
    A[用户搜索查询] --> B[Google Custom Search API]
    B --> C[Reddit帖子URL提取]
    C --> D[Reddit API数据获取]
    D --> E[帖子内容和评论分析]
    E --> F[评论者历史数据获取]
    F --> G[用户画像构建]
    G --> H[结果展示和保存]
```

### 三大核心步骤

| 步骤 | 功能描述 | 技术实现 |
|------|----------|----------|
| **步骤1: Google搜索** | 使用Google Custom Search API搜索Reddit相关内容 | Google Custom Search API + site:reddit.com过滤 |
| **步骤2: Reddit数据获取** | 并行获取Reddit帖子内容和评论数据 | Reddit API (PRAW) + 异步处理 |
| **步骤3: 用户分析** | 分析评论者历史，构建用户画像 | 用户历史数据分析 + 统计算法 |

---

## 🚀 快速开始

### 环境要求

- **Python**: 3.8+
- **系统**: Windows/Linux/macOS
- **网络**: 需要访问Google API和Reddit API

### 安装步骤

```bash
# 1. 克隆项目
git clone <repository-url>
cd CogBridges_v020

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置API密钥（三种方式任选其一）

# 方式1: 使用配置助手（推荐）
python setup_config.py

# 方式2: 手动配置
cp .env.example .env
# 然后编辑 .env 文件，填入API密钥

# 方式3: 直接编辑配置文件
# 编辑 config.py 文件中的相关配置项

# 4. 验证配置
python setup_config.py  # 检查配置状态

# 5. 启动应用
python start_cogbridges.py
```

### 验证安装

启动成功后，系统会自动：
- 启动后端API服务器 (默认端口: 5000)
- 启动前端Web服务器 (默认端口: 5001)
- 自动打开浏览器访问Web界面

访问 `http://localhost:5000/api/health` 检查API服务状态

---

## 🔧 API接口

### 核心搜索接口

#### 搜索分析接口

```http
POST /api/search
Content-Type: application/json

{
  "query": "搜索查询内容"
}
```

**响应示例**:
```json
{
  "success": true,
  "query": "Should I subscribe to GPT, Claude, Grok, or Gemini?",
  "session_id": "20250119_143022_a1b2c3d4",
  "timestamp": "2025-01-19T14:30:22.123456",
  "total_time": 45.67,
  "google_results": [
    {
      "title": "GPT vs Claude vs Grok comparison on Reddit",
      "url": "https://reddit.com/r/MachineLearning/comments/...",
      "snippet": "Discussion about AI model subscriptions...",
      "rank": 1
    }
  ],
  "reddit_posts": [
    {
      "title": "Which AI subscription is worth it?",
      "subreddit": "MachineLearning",
      "comments": [
        {
          "author": "ai_enthusiast",
          "body": "I've tried all four, here's my comparison...",
          "score": 156
        }
      ]
    }
  ],
  "commenters_history": {
    "ai_enthusiast": {
      "total_comments": 245,
      "total_posts": 12,
      "karma": 5420,
      "account_age_days": 892,
      "top_subreddits": ["MachineLearning", "artificial", "ChatGPT"]
    }
  },
  "statistics": {
    "google_search_time": 2.34,
    "reddit_posts_time": 18.45,
    "commenters_history_time": 24.88,
    "google_results_count": 5,
    "reddit_posts_count": 8,
    "commenters_count": 23
  }
}
```

#### 健康检查接口

```http
GET /api/health
```

**响应**:
```json
{
  "status": "healthy",
  "service": "CogBridges API",
  "timestamp": **********.123
}
```

### Python客户端示例

```python
import requests
import json

def search_reddit_analysis(query):
    """调用CogBridges搜索分析API"""
    url = "http://localhost:5000/api/search"

    response = requests.post(url, json={"query": query})

    if response.status_code == 200:
        result = response.json()
        print(f"✅ 搜索成功，耗时: {result['total_time']:.2f}秒")
        print(f"📊 找到 {result['statistics']['reddit_posts_count']} 个帖子")
        print(f"👥 分析 {result['statistics']['commenters_count']} 个用户")
        return result
    else:
        print(f"❌ 搜索失败: {response.status_code}")
        return None

# 使用示例
result = search_reddit_analysis("Should I subscribe to GPT, Claude, Grok, or Gemini?")
```

---

## 🧪 测试验证

### 运行完整集成测试

```bash
# 运行端到端集成测试
python test_integration_complete.py
```

该测试将执行完整的业务流程：
1. 使用模拟查询："Should I subscribe to GPT, Claude, Grok, or Gemini?"
2. 测试Google Custom Search API集成
3. 验证Reddit数据获取和分析
4. 检查用户画像构建功能
5. 生成详细的测试报告和性能指标

### 测试结果示例

```
🚀 CogBridges 完整集成测试
============================================================
📝 测试查询: Should I subscribe to GPT, Claude, Grok, or Gemini?
🕐 开始时间: 2025-01-19 14:30:22

🔧 初始化CogBridges服务...
✅ 服务初始化成功

🔍 开始执行完整业务流程...
✅ Google搜索: 找到 5 个结果
✅ Reddit帖子: 获取 8 个帖子
✅ 评论者历史: 分析 23 个用户
✅ 数据完整性: 所有步骤数据完整
✅ 性能指标: 总耗时 45.67秒 (< 120秒)

📊 验证结果: 5/5 项通过 (100.0%)
💾 测试结果已保存: test_results/integration_test_results_20250119_143022.json

============================================================
📋 测试总结
============================================================
✅ 总体结果: 成功
⏱️ 总耗时: 45.67秒

📊 业务流程详情:
  🔍 步骤1 - Google搜索: 2.34秒, 5个结果
  📝 步骤2 - Reddit帖子: 18.45秒, 8个帖子
  👥 步骤3 - 评论者历史: 24.88秒, 23个用户

⚡ 性能指标:
  平均每步耗时: 15.22秒
  数据获取效率: 0.8 项/秒

🎉 集成测试通过！系统运行正常。
```

### 性能基准

| 组件 | 平均响应时间 | 成功率 | 数据质量 |
|------|-------------|--------|----------|
| Google搜索 | 2-5秒 | 95%+ | 高质量Reddit链接 |
| Reddit数据获取 | 15-25秒 | 90%+ | 完整帖子和评论 |
| 用户画像分析 | 20-30秒 | 88%+ | 详细用户统计 |
| **完整流程** | **40-60秒** | **85%+** | **综合分析报告** |

---

## ⚙️ 配置说明

### API密钥配置

编辑 `config.py` 文件：

```python
# Google Custom Search API配置
GOOGLE_API_KEY = "your_google_api_key_here"
GOOGLE_SEARCH_ENGINE_ID = "your_search_engine_id_here"

# Reddit API配置
REDDIT_CLIENT_ID = "your_reddit_client_id_here"
REDDIT_CLIENT_SECRET = "your_reddit_client_secret_here"
REDDIT_USER_AGENT = "CogBridges/1.0 by YourUsername"

# 服务配置
HOST = "localhost"
PORT = 5000
FLASK_DEBUG = False

# 代理配置（可选）
HTTP_PROXY = "http://127.0.0.1:7890"  # 如需要
HTTPS_PROXY = "http://127.0.0.1:7890"  # 如需要
```

### 获取API密钥

#### Google Custom Search API
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 "Custom Search API"
4. 创建API密钥
5. 设置 [Custom Search Engine](https://cse.google.com/cse/)

#### Reddit API
1. 访问 [Reddit App Preferences](https://www.reddit.com/prefs/apps)
2. 点击 "Create App" 或 "Create Another App"
3. 选择 "script" 类型
4. 获取 client_id 和 client_secret

---

## 🚀 部署指南

### 本地开发

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置API密钥（编辑config.py）

# 3. 启动应用
python start_cogbridges.py
```

### 生产部署

#### 使用Gunicorn

```bash
# 安装Gunicorn
pip install gunicorn

# 启动生产服务
gunicorn -w 4 -b 0.0.0.0:5000 start_cogbridges:app
```

#### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "start_cogbridges.py"]
```

```bash
# 构建和运行
docker build -t cogbridges .
docker run -p 5000:5000 cogbridges
```

---

## 🔧 故障排查

### 常见问题

#### 1. API密钥配置问题

```bash
# 检查Google API配置
python -c "from config import config; print('Google API Key:', config.GOOGLE_API_KEY[:10] + '...')"

# 检查Reddit API配置
python -c "from config import config; print('Reddit Client ID:', config.REDDIT_CLIENT_ID)"
```

#### 2. 网络连接问题

```bash
# 测试Google API连接
curl "https://www.googleapis.com/customsearch/v1?key=YOUR_API_KEY&cx=YOUR_ENGINE_ID&q=test"

# 测试Reddit API连接
python -c "import praw; r = praw.Reddit(client_id='YOUR_ID', client_secret='YOUR_SECRET', user_agent='test'); print(r.read_only)"
```

#### 3. 依赖安装问题

```bash
# 重新安装依赖
pip install --upgrade -r requirements.txt

# 检查关键依赖
python -c "import flask, praw, requests, google.auth; print('All dependencies OK')"
```

### 性能优化建议

1. **API调用优化**：合理设置请求间隔，避免触发API限制
2. **并发控制**：根据网络条件调整并发数量
3. **缓存策略**：启用本地缓存减少重复API调用
4. **错误处理**：完善重试机制和错误恢复

---

## 📈 项目特点

### 真实的技术实现

- **Google Custom Search API**：使用官方API，不是网页爬虫
- **Reddit官方API**：通过PRAW库访问Reddit数据
- **异步处理**：高效的并发数据获取
- **完整业务流程**：从搜索到分析的端到端实现

### 实际应用场景

- **市场调研**：分析Reddit用户对产品/服务的讨论
- **用户研究**：了解特定话题的用户画像和观点
- **内容分析**：挖掘热门话题和用户互动模式
- **社区洞察**：分析Reddit社区的讨论趋势

---

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

---

## 📞 联系我们

- **项目主页**: https://github.com/your-org/cogbridges
- **问题反馈**: https://github.com/your-org/cogbridges/issues
- **技术支持**: 通过GitHub Issues提交问题

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

Made with ❤️ by CogBridges Team

**CogBridges** - 基于真实API的Reddit智能分析平台 🔍✨

</div>