"""
CogBridges Search - 子版块分类服务
使用LLM对子版块进行智能分类，识别同类型的子版块
"""

import asyncio
from typing import List, Dict, Any, Optional
from collections import Counter

from services.llm_service import llm_service
from utils.logger_utils import get_logger


class SubredditClassifier:
    """子版块分类器"""
    
    def __init__(self):
        """初始化分类器"""
        self.logger = get_logger(__name__)
        self.llm_service = llm_service
    
    async def classify_subreddits(
        self,
        subreddits: List[str],
        target_subreddit: str = None,
        max_categories: int = 10
    ) -> Dict[str, List[str]]:
        """
        对子版块进行分类
        
        Args:
            subreddits: 子版块列表
            target_subreddit: 目标子版块（用于参考分类）
            max_categories: 最大分类数量
            
        Returns:
            分类结果，格式为 {类别名: [子版块列表]}
        """
        if not subreddits:
            return {}
        
        if len(subreddits) == 1:
            return {"main": subreddits}
        
        self.logger.info(f"开始分类 {len(subreddits)} 个子版块")
        
        try:
            # 如果子版块太多，先进行预分组
            if len(subreddits) > 50:
                subreddits = await self._pre_filter_subreddits(subreddits, target_subreddit)
            
            # 使用LLM进行分类
            classification = await self.llm_service.classify_subreddits(
                subreddits, target_subreddit
            )
            
            # 后处理分类结果
            processed_classification = self._post_process_classification(
                classification, max_categories
            )
            
            self.logger.info(f"分类完成: {len(processed_classification)} 个类别")
            return processed_classification
            
        except Exception as e:
            self.logger.error(f"子版块分类失败: {e}")
            # 返回默认分类
            return self._create_default_classification(subreddits, target_subreddit)
    
    async def _pre_filter_subreddits(
        self,
        subreddits: List[str],
        target_subreddit: str = None,
        keep_count: int = 50
    ) -> List[str]:
        """
        预过滤子版块，保留最相关的
        
        Args:
            subreddits: 原始子版块列表
            target_subreddit: 目标子版块
            keep_count: 保留数量
            
        Returns:
            过滤后的子版块列表
        """
        self.logger.info(f"预过滤子版块: {len(subreddits)} -> {keep_count}")
        
        # 简单的启发式过滤
        filtered = []
        
        # 1. 如果有目标子版块，优先保留相关的
        if target_subreddit:
            target_lower = target_subreddit.lower()
            
            # 保留名称相似的
            similar_names = []
            for sr in subreddits:
                sr_lower = sr.lower()
                if (target_lower in sr_lower or sr_lower in target_lower or
                    any(word in sr_lower for word in target_lower.split('_'))):
                    similar_names.append(sr)
            
            filtered.extend(similar_names[:keep_count // 3])
        
        # 2. 保留常见的大型子版块
        popular_keywords = [
            'news', 'politics', 'technology', 'science', 'gaming', 'movies',
            'music', 'sports', 'funny', 'pics', 'videos', 'worldnews',
            'askreddit', 'todayilearned', 'explainlikeimfive', 'programming'
        ]
        
        popular_subreddits = []
        for sr in subreddits:
            sr_lower = sr.lower()
            if any(keyword in sr_lower for keyword in popular_keywords):
                popular_subreddits.append(sr)
        
        # 添加热门子版块，避免重复
        for sr in popular_subreddits:
            if sr not in filtered and len(filtered) < keep_count:
                filtered.append(sr)
        
        # 3. 随机添加其他子版块直到达到目标数量
        remaining = [sr for sr in subreddits if sr not in filtered]
        while len(filtered) < keep_count and remaining:
            filtered.append(remaining.pop(0))
        
        self.logger.info(f"预过滤完成: 保留 {len(filtered)} 个子版块")
        return filtered
    
    def _post_process_classification(
        self,
        classification: Dict[str, List[str]],
        max_categories: int
    ) -> Dict[str, List[str]]:
        """
        后处理分类结果
        
        Args:
            classification: 原始分类结果
            max_categories: 最大分类数量
            
        Returns:
            处理后的分类结果
        """
        if not classification:
            return {}
        
        # 1. 移除空分类
        filtered_classification = {
            category: subreddits 
            for category, subreddits in classification.items() 
            if subreddits
        }
        
        # 2. 如果分类太多，合并小分类
        if len(filtered_classification) > max_categories:
            # 按子版块数量排序
            sorted_categories = sorted(
                filtered_classification.items(),
                key=lambda x: len(x[1]),
                reverse=True
            )
            
            # 保留前N-1个大分类
            result = dict(sorted_categories[:max_categories-1])
            
            # 将剩余的合并为"其他"分类
            other_subreddits = []
            for category, subreddits in sorted_categories[max_categories-1:]:
                other_subreddits.extend(subreddits)
            
            if other_subreddits:
                result["其他"] = other_subreddits
            
            return result
        
        return filtered_classification
    
    def _create_default_classification(
        self,
        subreddits: List[str],
        target_subreddit: str = None
    ) -> Dict[str, List[str]]:
        """
        创建默认分类（当LLM分类失败时使用）
        
        Args:
            subreddits: 子版块列表
            target_subreddit: 目标子版块
            
        Returns:
            默认分类结果
        """
        self.logger.info("使用默认分类策略")
        
        if not subreddits:
            return {}
        
        # 简单的基于关键词的分类
        categories = {
            "技术": [],
            "娱乐": [],
            "新闻": [],
            "游戏": [],
            "体育": [],
            "其他": []
        }
        
        # 关键词映射
        keyword_mapping = {
            "技术": ["programming", "technology", "tech", "coding", "software", "computer", "ai", "ml"],
            "娱乐": ["movies", "music", "funny", "pics", "videos", "entertainment", "celebrity"],
            "新闻": ["news", "worldnews", "politics", "political"],
            "游戏": ["gaming", "games", "game", "xbox", "playstation", "nintendo"],
            "体育": ["sports", "football", "basketball", "soccer", "baseball", "hockey"]
        }
        
        # 分类子版块
        for subreddit in subreddits:
            subreddit_lower = subreddit.lower()
            classified = False
            
            for category, keywords in keyword_mapping.items():
                if any(keyword in subreddit_lower for keyword in keywords):
                    categories[category].append(subreddit)
                    classified = True
                    break
            
            if not classified:
                categories["其他"].append(subreddit)
        
        # 移除空分类
        result = {k: v for k, v in categories.items() if v}
        
        # 如果有目标子版块，将其相关的子版块单独分类
        if target_subreddit and target_subreddit in subreddits:
            target_category = f"与{target_subreddit}相关"
            target_related = [target_subreddit]
            
            # 从其他分类中移除目标子版块
            for category, srs in result.items():
                if target_subreddit in srs:
                    srs.remove(target_subreddit)
                    break
            
            result[target_category] = target_related
        
        return result
    
    def get_category_summary(
        self,
        classification: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """
        获取分类摘要信息
        
        Args:
            classification: 分类结果
            
        Returns:
            分类摘要
        """
        if not classification:
            return {}
        
        total_subreddits = sum(len(srs) for srs in classification.values())
        
        summary = {
            "total_categories": len(classification),
            "total_subreddits": total_subreddits,
            "categories": {}
        }
        
        for category, subreddits in classification.items():
            summary["categories"][category] = {
                "count": len(subreddits),
                "percentage": round(len(subreddits) / total_subreddits * 100, 1),
                "subreddits": subreddits[:5]  # 只显示前5个作为示例
            }
        
        return summary


# 创建全局分类器实例
subreddit_classifier = SubredditClassifier()
