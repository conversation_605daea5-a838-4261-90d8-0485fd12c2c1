"""
CogBridges Search - LLM服务
实现Replicate API调用，支持参数和prompt管理
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, Union
import replicate
from tenacity import retry, stop_after_attempt, wait_exponential

from config import config
from utils.logger_utils import get_logger, log_api_call
from utils.proxy_utils import setup_proxy


class LLMService:
    """LLM服务类，支持Replicate API调用"""
    
    def __init__(self):
        """初始化LLM服务"""
        self.logger = get_logger(__name__)
        
        # 检查配置
        self.configured = config.replicate_configured
        if not self.configured:
            self.logger.warning("Replicate API未配置，LLM服务不可用")
            return
        
        # 设置代理
        self.proxy_dict = setup_proxy()
        if self.proxy_dict:
            import os
            if "http" in self.proxy_dict:
                os.environ["HTTP_PROXY"] = self.proxy_dict["http"]
            if "https" in self.proxy_dict:
                os.environ["HTTPS_PROXY"] = self.proxy_dict["https"]
            self.logger.info(f"LLM服务使用代理: {self.proxy_dict}")
        
        # 初始化Replicate客户端
        self.client = replicate.Client(api_token=config.REPLICATE_API_TOKEN)
        
        # 请求统计
        self.request_count = 0
        self.total_request_time = 0.0
        
        self.logger.info("LLM服务初始化成功")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    @log_api_call("Replicate", "run", "POST")
    async def generate_text(
        self,
        prompt: str,
        model: str = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        system_prompt: str = None,
        **kwargs
    ) -> Optional[str]:
        """
        生成文本
        
        Args:
            prompt: 用户提示
            model: 模型名称，默认使用配置中的模型
            max_tokens: 最大token数
            temperature: 温度参数
            system_prompt: 系统提示
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        if not self.configured:
            raise ValueError("Replicate API未配置，无法使用LLM服务")
        
        start_time = time.time()
        model = model or config.REPLICATE_MODEL
        
        try:
            # 构建输入参数
            input_data = {
                "prompt": prompt,
                "max_tokens": max_tokens,
                "temperature": temperature,
                **kwargs
            }
            
            # 添加系统提示（如果支持）
            if system_prompt:
                input_data["system_prompt"] = system_prompt
            
            self.logger.debug(f"调用LLM模型: {model}, prompt长度: {len(prompt)}")
            
            # 调用Replicate API
            output = await asyncio.to_thread(
                self.client.run,
                model,
                input=input_data
            )
            
            # 处理输出
            if isinstance(output, list):
                result = "".join(str(item) for item in output)
            else:
                result = str(output)
            
            # 更新统计
            self.request_count += 1
            self.total_request_time += time.time() - start_time
            
            self.logger.info(f"LLM调用成功，输出长度: {len(result)}")
            return result
            
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            raise
    
    async def classify_subreddits(
        self,
        subreddits: List[str],
        target_subreddit: str = None
    ) -> Dict[str, List[str]]:
        """
        对子版块进行归类
        
        Args:
            subreddits: 子版块列表
            target_subreddit: 目标子版块（用于参考）
            
        Returns:
            归类结果，格式为 {类别名: [子版块列表]}
        """
        if not subreddits:
            return {}
        
        # 构建分类提示
        prompt = self._build_classification_prompt(subreddits, target_subreddit)
        
        try:
            # 调用LLM进行分类
            response = await self.generate_text(
                prompt=prompt,
                temperature=0.3,  # 较低温度确保一致性
                max_tokens=2000
            )
            
            # 解析分类结果
            return self._parse_classification_result(response, subreddits)
            
        except Exception as e:
            self.logger.error(f"子版块分类失败: {e}")
            # 返回默认分类（所有子版块归为一类）
            return {"related": subreddits}
    
    def _build_classification_prompt(
        self,
        subreddits: List[str],
        target_subreddit: str = None
    ) -> str:
        """构建分类提示"""
        subreddit_list = "\n".join(f"- {sr}" for sr in subreddits)
        
        base_prompt = f"""请对以下Reddit子版块进行分类，将相似主题的子版块归为一类。

子版块列表：
{subreddit_list}

请按照以下格式返回分类结果：
类别1: subreddit1, subreddit2, subreddit3
类别2: subreddit4, subreddit5
类别3: subreddit6

分类原则：
1. 按主题相似性分类（如：技术类、娱乐类、新闻类等）
2. 每个类别至少包含1个子版块
3. 类别名称要简洁明确
4. 确保所有子版块都被分类"""
        
        if target_subreddit:
            base_prompt += f"\n\n参考子版块：{target_subreddit}（优先将与此子版块相关的归为一类）"
        
        return base_prompt
    
    def _parse_classification_result(
        self,
        response: str,
        original_subreddits: List[str]
    ) -> Dict[str, List[str]]:
        """解析分类结果"""
        result = {}
        unclassified = set(original_subreddits)
        
        try:
            lines = response.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if ':' in line:
                    category, subreddits_str = line.split(':', 1)
                    category = category.strip()
                    
                    # 提取子版块名称
                    subreddit_names = []
                    for sr in subreddits_str.split(','):
                        sr = sr.strip()
                        # 移除可能的前缀符号
                        sr = sr.lstrip('- ')
                        if sr in original_subreddits:
                            subreddit_names.append(sr)
                            unclassified.discard(sr)
                    
                    if subreddit_names:
                        result[category] = subreddit_names
            
            # 将未分类的子版块归为"其他"类
            if unclassified:
                result["其他"] = list(unclassified)
            
        except Exception as e:
            self.logger.error(f"解析分类结果失败: {e}")
            # 返回默认分类
            result = {"related": original_subreddits}
        
        return result
    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "configured": self.configured,
            "request_count": self.request_count,
            "total_request_time": self.total_request_time,
            "average_request_time": (
                self.total_request_time / self.request_count 
                if self.request_count > 0 else 0
            )
        }
    
    async def test_connection(self) -> bool:
        """测试Replicate API连接"""
        if not self.configured:
            return False

        try:
            # 简单的测试调用
            result = await self.generate_text(
                prompt="Hello",
                max_tokens=10,
                temperature=0.1
            )
            if result:
                self.logger.info("Replicate API连接测试成功")
                return True
            else:
                self.logger.error("Replicate API连接测试失败: 无响应")
                return False
        except Exception as e:
            self.logger.error(f"Replicate API连接测试失败: {e}")
            return False


# 创建全局LLM服务实例
llm_service = LLMService()
