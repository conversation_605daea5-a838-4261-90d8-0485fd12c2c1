{"timestamp": "2025-07-20T17:08:21.776822", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1h8ox94", "subreddit": "singularity", "title": "How does <PERSON>, Grok or Llama compare to GPT or C", "score": 16, "num_comments": 13}, "timestamp": "2025-07-20T17:08:21.776821"}}
{"timestamp": "2025-07-20T17:08:21.865978", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1ih0eia", "subreddit": "Bard", "title": "<PERSON> vs <PERSON> vs ChatGP<PERSON> vs Deepseek: Who is Ac", "score": 47, "num_comments": 70}, "timestamp": "2025-07-20T17:08:21.865977"}}
{"timestamp": "2025-07-20T17:08:22.022594", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1lb0sdv", "subreddit": "grok", "title": "Should I subscribe for chatGPT or Grok?", "score": 15, "num_comments": 50}, "timestamp": "2025-07-20T17:08:22.022594"}}
{"timestamp": "2025-07-20T17:08:22.775120", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dqj1lg", "subreddit": "ClaudeAI", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspec", "score": 226, "num_comments": 139}, "timestamp": "2025-07-20T17:08:22.775120"}}
{"timestamp": "2025-07-20T17:08:23.109762", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1h8ox94", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:08:23.109762"}}
{"timestamp": "2025-07-20T17:08:23.692161", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1ih0eia", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:08:23.692161"}}
{"timestamp": "2025-07-20T17:08:23.742297", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1jnl8pn", "subreddit": "ClaudeAI", "title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ", "score": 558, "num_comments": 161}, "timestamp": "2025-07-20T17:08:23.742297"}}
{"timestamp": "2025-07-20T17:08:24.140311", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1lb0sdv", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:08:24.140310"}}
{"timestamp": "2025-07-20T17:08:26.041123", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dqj1lg", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:08:26.041123"}}
{"timestamp": "2025-07-20T17:08:26.861329", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1jnl8pn", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:08:26.861328"}}
{"timestamp": "2025-07-20T17:08:28.572741", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "irukadesune", "subreddit": null, "posts_count": 0, "comments_count": 0, "subreddit_activity": 0}, "timestamp": "2025-07-20T17:08:28.572740"}}
{"timestamp": "2025-07-20T17:08:34.487804", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Sad_Run_9798", "subreddit": null, "posts_count": 0, "comments_count": 0, "subreddit_activity": 0}, "timestamp": "2025-07-20T17:08:34.486950"}}
{"timestamp": "2025-07-20T17:10:25.268147", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1h8ox94", "subreddit": "singularity", "title": "How does Gemini, Grok or Llama compare to GPT or C", "score": 16, "num_comments": 13}, "timestamp": "2025-07-20T17:10:25.268147"}}
{"timestamp": "2025-07-20T17:10:27.472556", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1ih0eia", "subreddit": "Bard", "title": "Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac", "score": 44, "num_comments": 70}, "timestamp": "2025-07-20T17:10:27.472555"}}
{"timestamp": "2025-07-20T17:10:27.996279", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dqj1lg", "subreddit": "ClaudeAI", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspec", "score": 229, "num_comments": 139}, "timestamp": "2025-07-20T17:10:27.996279"}}
{"timestamp": "2025-07-20T17:10:28.905942", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1lb0sdv", "subreddit": "grok", "title": "Should I subscribe for chatGPT or Grok?", "score": 18, "num_comments": 50}, "timestamp": "2025-07-20T17:10:28.905942"}}
{"timestamp": "2025-07-20T17:10:33.850652", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1jnl8pn", "subreddit": "ClaudeAI", "title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ", "score": 556, "num_comments": 161}, "timestamp": "2025-07-20T17:10:33.850652"}}
{"timestamp": "2025-07-20T17:10:39.903863", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1h8ox94", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:10:39.903863"}}
{"timestamp": "2025-07-20T17:10:43.833673", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1ih0eia", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:10:43.833673"}}
{"timestamp": "2025-07-20T17:10:44.889400", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1lb0sdv", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:10:44.889399"}}
{"timestamp": "2025-07-20T17:10:45.135251", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dqj1lg", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:10:45.135251"}}
{"timestamp": "2025-07-20T17:10:49.617917", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1jnl8pn", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:10:49.617916"}}
