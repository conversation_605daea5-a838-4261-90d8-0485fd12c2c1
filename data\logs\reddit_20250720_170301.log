{"timestamp": "2025-07-20T17:03:01.568710", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1lb0sdv", "subreddit": "grok", "title": "Should I subscribe for chatGPT or Grok?", "score": 15, "num_comments": 50}, "timestamp": "2025-07-20T17:03:01.568710"}}
{"timestamp": "2025-07-20T17:03:01.902725", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1h8ox94", "subreddit": "singularity", "title": "How does <PERSON>, <PERSON><PERSON> or L<PERSON><PERSON> compare to GPT or C", "score": 17, "num_comments": 13}, "timestamp": "2025-07-20T17:03:01.902724"}}
{"timestamp": "2025-07-20T17:03:03.245731", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1lb0sdv", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:03:03.245730"}}
{"timestamp": "2025-07-20T17:03:03.386383", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1ih0eia", "subreddit": "Bard", "title": "Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac", "score": 46, "num_comments": 70}, "timestamp": "2025-07-20T17:03:03.386382"}}
{"timestamp": "2025-07-20T17:03:03.532684", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1jnl8pn", "subreddit": "ClaudeAI", "title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ", "score": 556, "num_comments": 161}, "timestamp": "2025-07-20T17:03:03.532684"}}
{"timestamp": "2025-07-20T17:03:03.771943", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1h8ox94", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:03:03.771943"}}
{"timestamp": "2025-07-20T17:03:03.912308", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dqj1lg", "subreddit": "ClaudeAI", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspec", "score": 227, "num_comments": 139}, "timestamp": "2025-07-20T17:03:03.912308"}}
{"timestamp": "2025-07-20T17:03:05.366249", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1ih0eia", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:03:05.366249"}}
{"timestamp": "2025-07-20T17:03:06.108073", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dqj1lg", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:03:06.108072"}}
{"timestamp": "2025-07-20T17:03:06.831260", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1jnl8pn", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:03:06.831259"}}
{"timestamp": "2025-07-20T17:03:08.177866", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "irukadesune", "subreddit": null, "posts_count": 0, "comments_count": 0, "subreddit_activity": 0}, "timestamp": "2025-07-20T17:03:08.177866"}}
{"timestamp": "2025-07-20T17:03:10.777857", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Sad_Run_9798", "subreddit": null, "posts_count": 0, "comments_count": 0, "subreddit_activity": 0}, "timestamp": "2025-07-20T17:03:10.777856"}}
{"timestamp": "2025-07-20T17:03:41.804812", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Cagnazzo82", "subreddit": null, "posts_count": 31, "comments_count": 100, "subreddit_activity": 19}, "timestamp": "2025-07-20T17:03:41.804812"}}
{"timestamp": "2025-07-20T17:03:41.838552", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "devonschmidt", "subreddit": null, "posts_count": 45, "comments_count": 98, "subreddit_activity": 30}, "timestamp": "2025-07-20T17:03:41.838552"}}
{"timestamp": "2025-07-20T17:03:41.873496", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Solarka45", "subreddit": null, "posts_count": 44, "comments_count": 100, "subreddit_activity": 45}, "timestamp": "2025-07-20T17:03:41.873495"}}
{"timestamp": "2025-07-20T17:03:41.894767", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Kathane37", "subreddit": null, "posts_count": 21, "comments_count": 98, "subreddit_activity": 30}, "timestamp": "2025-07-20T17:03:41.894767"}}
{"timestamp": "2025-07-20T17:03:41.925971", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Heavy_Hunt7860", "subreddit": null, "posts_count": 31, "comments_count": 100, "subreddit_activity": 40}, "timestamp": "2025-07-20T17:03:41.925970"}}
{"timestamp": "2025-07-20T17:03:41.973173", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "OfficeSalamander", "subreddit": null, "posts_count": 32, "comments_count": 99, "subreddit_activity": 44}, "timestamp": "2025-07-20T17:03:41.973173"}}
{"timestamp": "2025-07-20T17:03:41.994774", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "MrBietola", "subreddit": null, "posts_count": 42, "comments_count": 99, "subreddit_activity": 20}, "timestamp": "2025-07-20T17:03:41.994774"}}
{"timestamp": "2025-07-20T17:03:42.022695", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "focal-fossa", "subreddit": null, "posts_count": 2, "comments_count": 96, "subreddit_activity": 14}, "timestamp": "2025-07-20T17:03:42.022694"}}
{"timestamp": "2025-07-20T17:03:42.069026", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "MarinatedTechnician", "subreddit": null, "posts_count": 36, "comments_count": 100, "subreddit_activity": 46}, "timestamp": "2025-07-20T17:03:42.069026"}}
