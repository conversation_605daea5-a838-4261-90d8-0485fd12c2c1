{"timestamp": "2025-07-20T16:55:35.642078", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "rr1n6f", "subreddit": "learnprogramming", "title": "Learn Python or JavaScript first?", "score": 49, "num_comments": 37}, "timestamp": "2025-07-20T16:55:35.642078"}}
{"timestamp": "2025-07-20T16:55:35.705903", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dd2zcc", "subreddit": "learnprogramming", "title": "What should I learn Python or JavaScript ", "score": 32, "num_comments": 57}, "timestamp": "2025-07-20T16:55:35.705903"}}
{"timestamp": "2025-07-20T16:55:36.542994", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "qdllbu", "subreddit": "learnpython", "title": "Learning Javascript after Python", "score": 196, "num_comments": 74}, "timestamp": "2025-07-20T16:55:36.542994"}}
{"timestamp": "2025-07-20T16:55:37.270962", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "rr1n6f", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T16:55:37.270961"}}
{"timestamp": "2025-07-20T16:55:37.569914", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "b4ce2b", "subreddit": "learnprogramming", "title": "Is it okay to learn Python and Javascript at the s", "score": 8, "num_comments": 5}, "timestamp": "2025-07-20T16:55:37.569914"}}
{"timestamp": "2025-07-20T16:55:37.608093", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1ap5leh", "subreddit": "learnprogramming", "title": "Should I learn JavaScript or Python as a beginner", "score": 6, "num_comments": 23}, "timestamp": "2025-07-20T16:55:37.608092"}}
{"timestamp": "2025-07-20T16:55:38.697403", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dd2zcc", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T16:55:38.697403"}}
{"timestamp": "2025-07-20T16:55:38.852408", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "b4ce2b", "comments_count": 5, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T16:55:38.852407"}}
{"timestamp": "2025-07-20T16:55:39.197537", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1ap5leh", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T16:55:39.197536"}}
{"timestamp": "2025-07-20T16:55:39.435880", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "qdllbu", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T16:55:39.435880"}}
{"timestamp": "2025-07-20T16:56:15.155751", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "BinnyBit", "subreddit": null, "posts_count": 494, "comments_count": 100, "subreddit_activity": 86}, "timestamp": "2025-07-20T16:56:15.155751"}}
{"timestamp": "2025-07-20T16:56:15.190081", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "jamescodesthings", "subreddit": null, "posts_count": 37, "comments_count": 99, "subreddit_activity": 36}, "timestamp": "2025-07-20T16:56:15.190080"}}
{"timestamp": "2025-07-20T16:56:15.223838", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "actuallyalys", "subreddit": null, "posts_count": 5, "comments_count": 99, "subreddit_activity": 20}, "timestamp": "2025-07-20T16:56:15.223838"}}
{"timestamp": "2025-07-20T16:56:15.245222", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "ingframin", "subreddit": null, "posts_count": 30, "comments_count": 97, "subreddit_activity": 59}, "timestamp": "2025-07-20T16:56:15.245221"}}
{"timestamp": "2025-07-20T16:56:15.279999", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Xenofan23", "subreddit": null, "posts_count": 42, "comments_count": 100, "subreddit_activity": 38}, "timestamp": "2025-07-20T16:56:15.279998"}}
{"timestamp": "2025-07-20T16:56:15.282292", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "demvo2", "subreddit": null, "posts_count": 1, "comments_count": 26, "subreddit_activity": 6}, "timestamp": "2025-07-20T16:56:15.282292"}}
{"timestamp": "2025-07-20T16:56:15.315605", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Electrical-Ruin-8023", "subreddit": null, "posts_count": 0, "comments_count": 99, "subreddit_activity": 4}, "timestamp": "2025-07-20T16:56:15.315605"}}
{"timestamp": "2025-07-20T16:56:15.341941", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Zukarukite", "subreddit": null, "posts_count": 30, "comments_count": 97, "subreddit_activity": 59}, "timestamp": "2025-07-20T16:56:15.341941"}}
{"timestamp": "2025-07-20T16:56:15.364542", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "_memelorddotjpeg_", "subreddit": null, "posts_count": 90, "comments_count": 86, "subreddit_activity": 77}, "timestamp": "2025-07-20T16:56:15.364541"}}
{"timestamp": "2025-07-20T16:56:15.389823", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Zigglie", "subreddit": null, "posts_count": 4, "comments_count": 99, "subreddit_activity": 12}, "timestamp": "2025-07-20T16:56:15.389822"}}
