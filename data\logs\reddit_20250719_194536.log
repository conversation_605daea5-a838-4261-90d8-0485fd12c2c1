{"timestamp": "2025-07-19T19:45:36.018087", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1h8ox94", "subreddit": "singularity", "title": "How does Gemini, Grok or Llama compare to GPT or C", "score": 17, "num_comments": 13}, "timestamp": "2025-07-19T19:45:36.018086"}}
{"timestamp": "2025-07-19T19:45:36.650346", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1j42134", "subreddit": "grok", "title": "Would you say Grok 3 is better than GPT 4.5 or Cla", "score": 32, "num_comments": 77}, "timestamp": "2025-07-19T19:45:36.650345"}}
{"timestamp": "2025-07-19T19:45:36.864642", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1lwdzjd", "subreddit": "singularity", "title": "SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude", "score": 323, "num_comments": 90}, "timestamp": "2025-07-19T19:45:36.864642"}}
{"timestamp": "2025-07-19T19:45:37.387631", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1h8ox94", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T19:45:37.387631"}}
{"timestamp": "2025-07-19T19:45:37.578290", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dqj1lg", "subreddit": "ClaudeAI", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspec", "score": 230, "num_comments": 139}, "timestamp": "2025-07-19T19:45:37.578289"}}
{"timestamp": "2025-07-19T19:45:38.075269", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1jnl8pn", "subreddit": "ClaudeAI", "title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ", "score": 550, "num_comments": 162}, "timestamp": "2025-07-19T19:45:38.075268"}}
{"timestamp": "2025-07-19T19:45:38.808967", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1lwdzjd", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T19:45:38.808966"}}
{"timestamp": "2025-07-19T19:45:39.840734", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1j42134", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T19:45:39.840734"}}
{"timestamp": "2025-07-19T19:45:40.418228", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dqj1lg", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T19:45:40.418228"}}
{"timestamp": "2025-07-19T19:45:41.424314", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1jnl8pn", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-19T19:45:41.424313"}}
