# CogBridges 子版块分类功能

## 功能概述

本次更新为CogBridges添加了智能子版块分类功能，通过LLM对Reddit用户的历史活动进行分析和归类，提供更精准的用户画像分析。

## 新增功能

### 1. LLM服务模块 (`services/llm_service.py`)
- 集成Replicate API，支持GPT-4.1-mini模型
- 提供文本生成和子版块分类功能
- 支持代理配置和错误重试机制
- 包含完整的参数和prompt管理

### 2. 子版块分类器 (`services/subreddit_classifier.py`)
- 智能分类Reddit子版块
- 支持预过滤和后处理优化
- 提供分类摘要和统计信息
- 包含默认分类策略作为备选方案

### 3. 扩展的Reddit服务功能
- 从用户历史中提取子版块列表
- 支持多用户并行子版块提取
- 按分类后的子版块获取用户历史内容
- 优化的并发处理和错误处理

### 4. 增强的CogBridges主服务
- 新增 `search_with_subreddit_classification()` 方法
- 完整的5步骤工作流程：
  1. Google搜索
  2. Reddit帖子获取
  3. 评论者子版块提取
  4. 子版块智能分类
  5. 按分类获取用户历史
- 支持分类结果的保存和统计

## 工作流程

```
用户查询
    ↓
Google搜索Reddit内容
    ↓
获取Reddit帖子和评论
    ↓
提取所有评论者用户名
    ↓
并行获取用户历史数据 (1k posts + 1k comments)
    ↓
提取所有用户涉及的子版块列表
    ↓
使用LLM对子版块进行智能分类
    ↓
按分类后的子版块获取用户详细历史
    ↓
返回结构化的分析结果
```

## 配置要求

### 环境变量
```bash
# Replicate API配置
REPLICATE_API_TOKEN=your_replicate_api_token
REPLICATE_MODEL=openai/gpt-4.1-mini

# Reddit API配置（已有）
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret

# 代理配置（可选）
USE_PROXY=true
PROXY_HOST=127.0.0.1
PROXY_PORT=7890
```

### 依赖包
```bash
pip install replicate
```

## 使用示例

### 基本使用
```python
from services.cogbridges_service import CogBridgesService

# 初始化服务
service = CogBridgesService()

# 执行带分类的搜索
result = await service.search_with_subreddit_classification(
    "Should I subscribe to GPT, Claude, Grok, or Gemini?"
)

# 查看分类结果
print(result.subreddit_classification)
print(result.classification_summary)
```

### 仅使用分类功能
```python
from services.subreddit_classifier import subreddit_classifier

# 对子版块进行分类
subreddits = ["programming", "python", "movies", "gaming", "news"]
classification = await subreddit_classifier.classify_subreddits(
    subreddits, 
    target_subreddit="programming"
)

print(classification)
# 输出: {
#   "技术类": ["programming", "python"],
#   "娱乐类": ["movies", "gaming"],
#   "新闻类": ["news"]
# }
```

## 测试

### 运行基本功能测试
```bash
python test_simple.py
```

### 运行完整功能测试
```bash
python test_subreddit_classification.py
```

### 运行演示
```bash
python demo_new_feature.py
```

## 性能特点

- **并行处理**: 支持多用户并行数据获取
- **智能分类**: 使用GPT-4.1-mini进行高质量分类
- **错误处理**: 完善的重试机制和降级策略
- **资源优化**: 合理的并发限制和请求频率控制
- **缓存友好**: 支持结果缓存和增量更新

## API限制

- Reddit API: 每分钟60次请求
- Replicate API: 根据订阅计划限制
- 建议使用代理以提高稳定性

## 输出格式

新的搜索结果包含以下额外字段：

```json
{
  "subreddit_classification": {
    "技术类": ["programming", "python", "javascript"],
    "娱乐类": ["movies", "gaming", "funny"],
    "新闻类": ["news", "worldnews", "politics"]
  },
  "classification_summary": {
    "total_categories": 3,
    "total_subreddits": 9,
    "categories": {
      "技术类": {
        "count": 3,
        "percentage": 33.3,
        "subreddits": ["programming", "python", "javascript"]
      }
    }
  },
  "commenters_history": {
    "username1": {
      "技术类": {
        "stats": {"total_posts": 5, "total_comments": 15},
        "posts": [...],
        "comments": [...]
      }
    }
  }
}
```

## 注意事项

1. **API密钥安全**: 请妥善保管Replicate API密钥
2. **网络稳定性**: 建议配置代理以提高API调用成功率
3. **资源消耗**: 新功能会增加API调用次数和处理时间
4. **数据量**: 每个用户最多获取1000条帖子和1000条评论
5. **分类准确性**: LLM分类结果可能因prompt和模型版本而异

## 故障排除

### 常见问题

1. **LLM服务连接失败**
   - 检查REPLICATE_API_TOKEN是否正确设置
   - 确认网络连接和代理配置

2. **分类结果不理想**
   - 调整分类prompt
   - 增加预过滤逻辑
   - 使用不同的温度参数

3. **Reddit API限制**
   - 降低并发数量
   - 增加请求间隔
   - 使用更稳定的网络连接

## 更新日志

### v2.0.0 (2025-07-20)
- ✅ 新增LLM服务模块
- ✅ 实现子版块智能分类
- ✅ 扩展Reddit服务功能
- ✅ 集成到主业务流程
- ✅ 完善测试和文档
