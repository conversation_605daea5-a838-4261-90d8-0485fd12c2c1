{"timestamp": "2025-07-20T17:04:15.862590", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1h8ox94", "subreddit": "singularity", "title": "How does Gemini, Grok or Llama compare to GPT or C", "score": 17, "num_comments": 13}, "timestamp": "2025-07-20T17:04:15.862590"}}
{"timestamp": "2025-07-20T17:04:16.826190", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1h8ox94", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:04:16.826189"}}
{"timestamp": "2025-07-20T17:04:17.058406", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1lb0sdv", "subreddit": "grok", "title": "Should I subscribe for chatGPT or Grok?", "score": 15, "num_comments": 50}, "timestamp": "2025-07-20T17:04:17.058405"}}
{"timestamp": "2025-07-20T17:04:17.928791", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dqj1lg", "subreddit": "ClaudeAI", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspec", "score": 228, "num_comments": 139}, "timestamp": "2025-07-20T17:04:17.928790"}}
{"timestamp": "2025-07-20T17:04:17.998551", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1jnl8pn", "subreddit": "ClaudeAI", "title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ", "score": 552, "num_comments": 161}, "timestamp": "2025-07-20T17:04:17.998551"}}
{"timestamp": "2025-07-20T17:04:18.665158", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1lb0sdv", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:04:18.665158"}}
{"timestamp": "2025-07-20T17:04:20.782808", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dqj1lg", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:04:20.782807"}}
{"timestamp": "2025-07-20T17:04:21.097684", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1jnl8pn", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:04:21.097684"}}
{"timestamp": "2025-07-20T17:04:21.442225", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1ih0eia", "subreddit": "Bard", "title": "Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac", "score": 44, "num_comments": 70}, "timestamp": "2025-07-20T17:04:21.442225"}}
{"timestamp": "2025-07-20T17:04:23.212293", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1ih0eia", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:04:23.212293"}}
{"timestamp": "2025-07-20T17:04:24.574742", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "irukadesune", "subreddit": null, "posts_count": 0, "comments_count": 0, "subreddit_activity": 0}, "timestamp": "2025-07-20T17:04:24.573734"}}
{"timestamp": "2025-07-20T17:04:24.718420", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Sad_Run_9798", "subreddit": null, "posts_count": 0, "comments_count": 0, "subreddit_activity": 0}, "timestamp": "2025-07-20T17:04:24.718420"}}
{"timestamp": "2025-07-20T17:04:46.589504", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_user_history", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_user_history", "data": {"username": "Spiritual-Extent1105", "subreddit": null, "posts_count": 2, "comments_count": 7, "subreddit_activity": 6}, "timestamp": "2025-07-20T17:04:46.589504"}}
{"timestamp": "2025-07-20T17:06:22.322634", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1ih0eia", "subreddit": "Bard", "title": "Gemini vs Claude vs ChatGPT vs Deepseek: Who is Ac", "score": 44, "num_comments": 70}, "timestamp": "2025-07-20T17:06:22.322633"}}
{"timestamp": "2025-07-20T17:06:23.262387", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1h8ox94", "subreddit": "singularity", "title": "How does Gemini, Grok or Llama compare to GPT or C", "score": 17, "num_comments": 13}, "timestamp": "2025-07-20T17:06:23.262386"}}
{"timestamp": "2025-07-20T17:06:25.770395", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1lb0sdv", "subreddit": "grok", "title": "Should I subscribe for chatGPT or Grok?", "score": 16, "num_comments": 50}, "timestamp": "2025-07-20T17:06:25.770395"}}
{"timestamp": "2025-07-20T17:06:26.367830", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1dqj1lg", "subreddit": "ClaudeAI", "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspec", "score": 226, "num_comments": 139}, "timestamp": "2025-07-20T17:06:26.367830"}}
{"timestamp": "2025-07-20T17:06:28.305526", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_post", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_post", "data": {"post_id": "1jnl8pn", "subreddit": "ClaudeAI", "title": "I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ", "score": 550, "num_comments": 161}, "timestamp": "2025-07-20T17:06:28.305525"}}
{"timestamp": "2025-07-20T17:06:39.920391", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1ih0eia", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:06:39.920391"}}
{"timestamp": "2025-07-20T17:06:40.453669", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1h8ox94", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:06:40.453669"}}
{"timestamp": "2025-07-20T17:06:42.909333", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1lb0sdv", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:06:42.909332"}}
{"timestamp": "2025-07-20T17:06:44.772373", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1dqj1lg", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:06:44.772373"}}
{"timestamp": "2025-07-20T17:06:46.941741", "level": "INFO", "logger": "operation.reddit", "message": "Reddit操作: get_comments", "module": "logger_utils", "function": "log_reddit_operation", "line": 273, "extra": {"operation": "get_comments", "data": {"post_id": "1jnl8pn", "comments_count": 6, "sort": "top", "limit": 6}, "timestamp": "2025-07-20T17:06:46.941740"}}
